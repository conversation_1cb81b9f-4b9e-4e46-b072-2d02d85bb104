#!/usr/bin/env python3
"""
测试BSIM3在GUI场景下的拟合问题
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import fitting_core
import config
import data_utils

def test_bsim3_with_real_data():
    """使用真实数据测试BSIM3拟合"""
    print("🧪 使用真实数据测试BSIM3拟合")
    print("=" * 50)
    
    # 测试IdVg数据
    print("1. 测试BSIM3 IdVg拟合...")
    try:
        # 加载真实IdVg数据
        idvg_data = pd.read_csv('real_data.csv')
        print(f"   加载IdVg数据: {len(idvg_data)} 个数据点")
        
        # 使用GUI默认的BSIM3参数设置
        model_params = config.get_model_params("BSIM3")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置拟合参数（模拟GUI中的选择）
        params_to_fit = [
            {'name': 'vth0', 'initial': 2.8, 'lower': 1.0, 'upper': 5.0},
            {'name': 'u0', 'initial': 0.06, 'lower': 0.01, 'upper': 0.1},
            {'name': 'vsat', 'initial': 1e5, 'lower': 1e4, 'upper': 5e5}
        ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        print(f"   初始参数: {all_initial_params}")
        print(f"   拟合参数: {[p['name'] for p in params_to_fit]}")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, idvg_data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        print("   ✅ IdVg拟合完成")
        print("   最终参数:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"     {name}: {value:.6f} (初始: {initial:.6f})")
        
        # 计算拟合质量
        vgs_real = idvg_data['Vgs'].values
        id_real = idvg_data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"   拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.semilogy(vgs_real, id_real, 'bo', markersize=4, label='Real Data')
        plt.semilogy(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVg Fitting (Log Scale)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        plt.plot(vgs_real, id_real, 'bo', markersize=4, label='Real Data')
        plt.plot(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVg Fitting (Linear Scale)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('bsim3_idvg_test.png', dpi=150, bbox_inches='tight')
        print("   📊 IdVg拟合结果保存为 'bsim3_idvg_test.png'")
        
    except Exception as e:
        print(f"   ❌ IdVg拟合失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试IdVd数据
    print("\n2. 测试BSIM3 IdVd拟合...")
    try:
        # 加载真实IdVd数据
        idvd_data = pd.read_csv('real_data_idvd.csv')
        print(f"   加载IdVd数据: {len(idvd_data)} 个数据点")
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvd")
        template = fitting_core.get_model_template("BSIM3", "idvd")
        
        # 执行拟合
        final_params, vds_fit, id_fit = fitting_core.perform_fitting(
            template, idvd_data, sim_params['vgs'],
            (sim_params['vds_start'], sim_params['vds_stop'], sim_params['vds_step']),
            params_to_fit, all_initial_params, "idvd"
        )
        
        print("   ✅ IdVd拟合完成")
        print("   最终参数:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"     {name}: {value:.6f} (初始: {initial:.6f})")
        
        # 计算拟合质量
        vds_real = idvd_data['Vds'].values
        id_real = idvd_data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vds_real, vds_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"   拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(vds_real, id_real, 'bo', markersize=4, label='Real Data')
        plt.plot(vds_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vds (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVd Fitting')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        residuals = id_fit_interp - id_real
        plt.plot(vds_real, residuals, 'go', markersize=3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('Vds (V)')
        plt.ylabel('Residuals (A)')
        plt.title('Fitting Residuals')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('bsim3_idvd_test.png', dpi=150, bbox_inches='tight')
        print("   📊 IdVd拟合结果保存为 'bsim3_idvd_test.png'")
        
    except Exception as e:
        print(f"   ❌ IdVd拟合失败: {e}")
        import traceback
        traceback.print_exc()

def test_bsim3_parameter_bounds():
    """测试BSIM3参数边界问题"""
    print("\n🔍 测试BSIM3参数边界问题")
    print("=" * 50)
    
    # 获取默认参数配置
    model_params = config.get_model_params("BSIM3")
    
    print("当前BSIM3参数配置:")
    for name, params in model_params.items():
        print(f"  {name}: 初始={params['initial']}, 范围=[{params['lower']}, {params['upper']}]")
    
    # 检查参数是否合理
    print("\n参数合理性检查:")
    
    # 检查VTH0
    vth0_config = model_params['vth0']
    if vth0_config['initial'] < 0 or vth0_config['initial'] > 10:
        print("  ⚠️ VTH0初始值可能不合理")
    else:
        print("  ✅ VTH0参数合理")
    
    # 检查U0
    u0_config = model_params['u0']
    if u0_config['initial'] < 0.001 or u0_config['initial'] > 1:
        print("  ⚠️ U0初始值可能不合理")
    else:
        print("  ✅ U0参数合理")
    
    # 检查VSAT
    vsat_config = model_params['vsat']
    if vsat_config['initial'] < 1e3 or vsat_config['initial'] > 1e7:
        print("  ⚠️ VSAT初始值可能不合理")
    else:
        print("  ✅ VSAT参数合理")

def test_bsim3_convergence():
    """测试BSIM3收敛性问题"""
    print("\n🎯 测试BSIM3收敛性")
    print("=" * 50)
    
    # 加载数据
    try:
        idvg_data = pd.read_csv('real_data.csv')
    except:
        print("❌ 无法加载测试数据")
        return
    
    # 测试不同的初始值
    initial_value_tests = [
        {'vth0': 1.0, 'u0': 0.03, 'vsat': 5e4},
        {'vth0': 2.8, 'u0': 0.06, 'vsat': 1e5},  # 默认值
        {'vth0': 4.0, 'u0': 0.09, 'vsat': 2e5},
    ]
    
    model_params = config.get_model_params("BSIM3")
    sim_params = config.get_simulation_params("idvg")
    template = fitting_core.get_model_template("BSIM3", "idvg")
    
    for i, test_values in enumerate(initial_value_tests):
        print(f"\n测试初始值组合 {i+1}: {test_values}")
        
        # 设置初始参数
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        all_initial_params.update(test_values)
        
        # 设置拟合参数
        params_to_fit = [
            {'name': 'vth0', 'initial': test_values['vth0'], 'lower': 1.0, 'upper': 5.0},
            {'name': 'u0', 'initial': test_values['u0'], 'lower': 0.01, 'upper': 0.1}
        ]
        
        try:
            final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
                template, idvg_data, sim_params['vds'],
                (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
                params_to_fit, all_initial_params, "idvg"
            )
            
            # 计算拟合质量
            vgs_real = idvg_data['Vgs'].values
            id_real = idvg_data['Id'].values
            id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
            rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
            
            print(f"  ✅ 收敛成功, RMSE = {rmse:.6f}")
            print(f"     最终参数: vth0={final_params['vth0']:.3f}, u0={final_params['u0']:.6f}")
            
        except Exception as e:
            print(f"  ❌ 收敛失败: {e}")

def main():
    """主测试函数"""
    print("🔧 BSIM3 GUI场景测试")
    print("=" * 60)
    
    # 检查必要文件
    import os
    required_files = ['real_data.csv', 'real_data_idvd.csv']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("请先运行数据生成脚本:")
        print("  uv run python 1_generate_real_data.py")
        print("  uv run python 2_generate_idvd_data.py")
        return
    
    # 执行测试
    test_bsim3_with_real_data()
    test_bsim3_parameter_bounds()
    test_bsim3_convergence()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("如果所有测试都通过，那么BSIM3拟合功能本身是正常的。")
    print("问题可能出现在:")
    print("1. GUI界面的参数设置")
    print("2. 用户选择的参数组合")
    print("3. 数据质量或格式")
    print("4. 拟合算法的收敛设置")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("测试程序退出")
