#!/usr/bin/env python3
"""
测试IdVd拟合功能的脚本
"""

import pandas as pd
import matplotlib.pyplot as plt
import fitting_core

def test_idvd_fitting():
    """测试IdVd拟合功能"""
    print("=== Testing IdVd Fitting ===")
    
    # 1. 加载IdVd数据
    print("1. Loading IdVd data...")
    try:
        real_data = pd.read_csv('real_data_idvd.csv')
        print(f"   Loaded {len(real_data)} data points")
        print(f"   Vds range: {real_data['Vds'].min():.1f} to {real_data['Vds'].max():.1f} V")
        print(f"   Id range: {real_data['Id'].min():.3f} to {real_data['Id'].max():.3f} A")
    except FileNotFoundError:
        print("   Error: real_data_idvd.csv not found. Please run 2_generate_idvd_data.py first.")
        return
    
    # 2. 设置拟合参数
    print("\n2. Setting up fitting parameters...")
    model_type = "LEVEL=1"
    fitting_type = "idvd"
    
    # 仿真参数
    fixed_voltage = 5.0  # Vgs = 5V
    sweep_params = (0.0, 20.0, 0.5)  # Vds从0到20V，步长0.5V
    
    # 模型参数
    all_initial_params = {
        'vto': 2.5,  # 初始阈值电压
        'kp': 0.1    # 初始跨导参数
    }
    
    # 待拟合参数
    params_to_fit = [
        {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
        {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
    ]
    
    print(f"   Model: {model_type}")
    print(f"   Fitting type: {fitting_type}")
    print(f"   Fixed Vgs: {fixed_voltage} V")
    print(f"   Vds sweep: {sweep_params[0]} to {sweep_params[1]} V, step {sweep_params[2]} V")
    print(f"   Parameters to fit: {[p['name'] for p in params_to_fit]}")
    
    # 3. 获取模板并测试初始仿真
    print("\n3. Testing initial simulation...")
    try:
        model_template = fitting_core.get_model_template(model_type, fitting_type)
        vds_sim, id_sim = fitting_core.run_spice_simulation(
            model_template, all_initial_params, sweep_params, fixed_voltage, fitting_type
        )
        
        if len(vds_sim) > 0:
            print(f"   Initial simulation successful: {len(vds_sim)} points")
            print(f"   Simulated Vds range: {vds_sim.min():.1f} to {vds_sim.max():.1f} V")
            print(f"   Simulated Id range: {id_sim.min():.3f} to {id_sim.max():.3f} A")
        else:
            print("   Error: Initial simulation failed")
            return
    except Exception as e:
        print(f"   Error in initial simulation: {e}")
        return
    
    # 4. 执行拟合
    print("\n4. Starting fitting process...")
    try:
        final_params, vds_fit, id_fit = fitting_core.perform_fitting(
            model_template, real_data, fixed_voltage, sweep_params, 
            params_to_fit, all_initial_params, fitting_type
        )
        
        print("   Fitting completed successfully!")
        print("\n   Final parameters:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"     {name}: {value:.6f} (initial: {initial:.6f})")
            else:
                print(f"     {name}: {value:.6f} (fixed)")
                
    except Exception as e:
        print(f"   Error during fitting: {e}")
        return
    
    # 5. 绘制结果
    print("\n5. Plotting results...")
    try:
        plt.figure(figsize=(10, 6))
        plt.plot(real_data['Vds'], real_data['Id'], 'bo', markersize=4, label='Target Data')
        plt.plot(vds_sim, id_sim, 'g--', linewidth=2, label='Initial Model')
        plt.plot(vds_fit, id_fit, 'r-', linewidth=2, label='Fitted Model')
        plt.xlabel('Vds (V)')
        plt.ylabel('Id (A)')
        plt.title(f'IdVd Fitting Results (Vgs = {fixed_voltage}V)')
        plt.grid(True)
        plt.legend()
        plt.tight_layout()
        plt.savefig('idvd_fitting_test.png', dpi=150, bbox_inches='tight')
        print("   Plot saved as 'idvd_fitting_test.png'")
        
        # 计算拟合误差
        id_real_interp = real_data['Id'].values
        id_fit_interp = id_fit
        if len(id_fit_interp) == len(id_real_interp):
            rmse = ((id_fit_interp - id_real_interp) ** 2).mean() ** 0.5
            print(f"   RMSE: {rmse:.6f} A")
        
    except Exception as e:
        print(f"   Error in plotting: {e}")
    
    print("\n=== IdVd Fitting Test Completed ===")

if __name__ == "__main__":
    test_idvd_fitting()
