#!/usr/bin/env python3
"""
进程清理工具 - 清理可能残留的GUI进程
"""

import os
import sys
import subprocess
import signal
import psutil
import time

def find_related_processes():
    """查找相关的Python进程"""
    related_processes = []
    current_pid = os.getpid()
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # 跳过当前进程
                if proc.info['pid'] == current_pid:
                    continue
                
                # 查找Python进程
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline:
                        cmdline_str = ' '.join(cmdline)
                        # 查找与项目相关的进程
                        if any(keyword in cmdline_str for keyword in [
                            'gui_app.py', 'test_gui_switching.py', 'verify_gui.py',
                            'mosfet_fitting', 'fitting_core', 'ngspice'
                        ]):
                            related_processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'cmdline': cmdline_str
                            })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
                
    except Exception as e:
        print(f"Error scanning processes: {e}")
    
    return related_processes

def kill_process_safely(pid):
    """安全地终止进程"""
    try:
        proc = psutil.Process(pid)
        
        # 首先尝试温和终止
        proc.terminate()
        
        # 等待进程结束
        try:
            proc.wait(timeout=3)
            return True, "Terminated gracefully"
        except psutil.TimeoutExpired:
            # 如果温和终止失败，强制杀死
            proc.kill()
            proc.wait(timeout=2)
            return True, "Force killed"
            
    except psutil.NoSuchProcess:
        return True, "Process already gone"
    except psutil.AccessDenied:
        return False, "Access denied"
    except Exception as e:
        return False, f"Error: {e}"

def cleanup_gui_processes():
    """清理GUI相关进程"""
    print("🧹 GUI进程清理工具")
    print("=" * 50)
    
    # 查找相关进程
    print("1. 扫描相关进程...")
    processes = find_related_processes()
    
    if not processes:
        print("   ✅ 没有发现相关的残留进程")
        return
    
    print(f"   发现 {len(processes)} 个相关进程:")
    for i, proc in enumerate(processes, 1):
        print(f"   {i}. PID: {proc['pid']}, 命令: {proc['cmdline'][:80]}...")
    
    # 询问用户是否清理
    print("\n2. 清理选项:")
    print("   a) 清理所有相关进程")
    print("   b) 选择性清理")
    print("   c) 取消")
    
    choice = input("\n请选择 (a/b/c): ").lower().strip()
    
    if choice == 'c':
        print("取消清理")
        return
    elif choice == 'a':
        # 清理所有进程
        print("\n3. 清理所有相关进程...")
        for proc in processes:
            success, message = kill_process_safely(proc['pid'])
            status = "✅" if success else "❌"
            print(f"   {status} PID {proc['pid']}: {message}")
    elif choice == 'b':
        # 选择性清理
        print("\n3. 选择要清理的进程 (输入进程编号，用空格分隔):")
        try:
            indices = input("进程编号: ").split()
            for idx_str in indices:
                idx = int(idx_str) - 1
                if 0 <= idx < len(processes):
                    proc = processes[idx]
                    success, message = kill_process_safely(proc['pid'])
                    status = "✅" if success else "❌"
                    print(f"   {status} PID {proc['pid']}: {message}")
                else:
                    print(f"   ❌ 无效的进程编号: {idx_str}")
        except ValueError:
            print("   ❌ 无效输入")
    else:
        print("   ❌ 无效选择")
        return
    
    # 验证清理结果
    print("\n4. 验证清理结果...")
    time.sleep(1)  # 等待进程完全退出
    remaining_processes = find_related_processes()
    
    if not remaining_processes:
        print("   ✅ 所有相关进程已清理完成")
    else:
        print(f"   ⚠️ 仍有 {len(remaining_processes)} 个进程残留:")
        for proc in remaining_processes:
            print(f"      PID: {proc['pid']}, 命令: {proc['cmdline'][:60]}...")

def show_process_monitor():
    """显示进程监控"""
    print("\n📊 进程监控模式 (按Ctrl+C退出)")
    print("-" * 50)
    
    try:
        while True:
            processes = find_related_processes()
            
            # 清屏 (在支持的终端中)
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print("🔍 实时进程监控")
            print("=" * 50)
            print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            if processes:
                print(f"\n发现 {len(processes)} 个相关进程:")
                for i, proc in enumerate(processes, 1):
                    print(f"{i:2d}. PID: {proc['pid']:6d} | {proc['cmdline'][:60]}...")
            else:
                print("\n✅ 没有发现相关进程")
            
            print("\n按Ctrl+C退出监控...")
            time.sleep(2)
            
    except KeyboardInterrupt:
        print("\n\n监控已停止")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--monitor':
        show_process_monitor()
    else:
        cleanup_gui_processes()
        
        # 询问是否启动监控
        monitor = input("\n是否启动进程监控? (y/n): ").lower().strip()
        if monitor == 'y':
            show_process_monitor()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序错误: {e}")
    finally:
        print("清理工具退出")
