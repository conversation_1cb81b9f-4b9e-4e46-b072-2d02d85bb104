#!/usr/bin/env python3
"""
测试GUI拟合类型切换功能
"""

import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def test_gui_switching():
    """测试GUI拟合类型切换"""
    
    # 创建主窗口
    root = tk.Tk()
    root.title("GUI Switching Test")
    root.geometry("800x600")
    
    # 创建拟合类型选择
    control_frame = ttk.Frame(root)
    control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10, pady=10)
    
    ttk.Label(control_frame, text="Fitting Type:").pack(pady=5)
    fitting_type_var = tk.StringVar(value="idvg")
    fitting_type_combo = ttk.Combobox(control_frame, textvariable=fitting_type_var, 
                                     values=["idvg", "idvd"], state="readonly")
    fitting_type_combo.pack(pady=5)
    
    # 创建图表
    plot_frame = ttk.Frame(root)
    plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    fig, ax = plt.subplots()
    canvas = FigureCanvasTkAgg(fig, master=plot_frame)
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # 状态标签
    status_label = ttk.Label(control_frame, text="Current: idvg")
    status_label.pack(pady=10)
    
    def on_fitting_type_change(event=None):
        """拟合类型改变时的处理"""
        fitting_type = fitting_type_var.get()
        
        # 清除图表
        ax.clear()
        
        # 根据拟合类型设置图表
        if fitting_type == "idvg":
            ax.set_title("Id-Vgs Curve")
            ax.set_xlabel("Vgs (V)")
            ax.set_ylabel("Id (A)")
            # 添加示例数据
            import numpy as np
            vgs = np.linspace(0, 10, 50)
            id_vals = np.where(vgs > 2.8, (vgs - 2.8) * 0.12 * 15, 1e-12)
            ax.plot(vgs, id_vals, 'b-', label='IdVg Example')
            status_label.config(text="Current: IdVg")
            
        elif fitting_type == "idvd":
            ax.set_title("Id-Vds Curve")
            ax.set_xlabel("Vds (V)")
            ax.set_ylabel("Id (A)")
            # 添加示例数据
            import numpy as np
            vds = np.linspace(0, 20, 50)
            vgs = 5.0
            vto = 2.8
            kp = 0.12
            if vgs > vto:
                id_vals = np.where(vds < (vgs - vto), 
                                 kp * ((vgs - vto) * vds - 0.5 * vds**2),
                                 0.5 * kp * (vgs - vto)**2)
            else:
                id_vals = np.zeros_like(vds)
            ax.plot(vds, id_vals, 'r-', label='IdVd Example')
            status_label.config(text="Current: IdVd")
        
        ax.grid(True)
        ax.legend()
        canvas.draw()
        
        print(f"Switched to: {fitting_type}")
    
    # 绑定事件
    fitting_type_combo.bind("<<ComboboxSelected>>", on_fitting_type_change)
    
    # 初始化图表
    on_fitting_type_change()
    
    # 添加切换按钮用于测试
    def switch_to_idvd():
        fitting_type_var.set("idvd")
        on_fitting_type_change()
    
    def switch_to_idvg():
        fitting_type_var.set("idvg")
        on_fitting_type_change()
    
    ttk.Button(control_frame, text="Switch to IdVd", command=switch_to_idvd).pack(pady=5)
    ttk.Button(control_frame, text="Switch to IdVg", command=switch_to_idvg).pack(pady=5)
    
    # 添加说明
    info_text = """
测试说明:
1. 使用下拉菜单切换拟合类型
2. 使用按钮快速切换
3. 观察图表标题和轴标签的变化
4. 检查示例数据的显示
    """
    ttk.Label(control_frame, text=info_text, justify=tk.LEFT).pack(pady=10)
    
    print("GUI切换测试启动")
    print("请测试拟合类型切换功能")
    
    root.mainloop()

if __name__ == "__main__":
    test_gui_switching()
