#!/usr/bin/env python3
"""
MOSFET拟合工具测试套件
验证所有功能的正确性和稳定性
"""

import unittest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path

# 导入要测试的模块
import fitting_core
import config
import data_utils
from exceptions import DataError, SimulationError

class TestFittingCore(unittest.TestCase):
    """测试fitting_core模块"""
    
    def setUp(self):
        """测试前准备"""
        self.model_type = "LEVEL=1"
        self.sim_params = {'vto': 2.8, 'kp': 0.12}
        
    def test_get_model_template(self):
        """测试模板获取功能"""
        # 测试有效的模板组合
        template = fitting_core.get_model_template("LEVEL=1", "idvg")
        self.assertIn("LEVEL = 1", template)
        self.assertIn("VTO", template)
        
        template = fitting_core.get_model_template("BSIM3", "idvd")
        self.assertIn("LEVEL = 8", template)
        self.assertIn("VTH0", template)
        
        # 测试无效组合
        with self.assertRaises(ValueError):
            fitting_core.get_model_template("INVALID", "idvg")
    
    def test_spice_simulation(self):
        """测试SPICE仿真功能"""
        template = fitting_core.get_model_template(self.model_type, "idvg")
        sweep_params = (0.0, 5.0, 1.0)
        fixed_voltage = 15.0
        
        voltage_sim, id_sim = fitting_core.run_spice_simulation(
            template, self.sim_params, sweep_params, fixed_voltage, "idvg"
        )
        
        # 验证仿真结果
        self.assertGreater(len(voltage_sim), 0, "Simulation should return data points")
        self.assertEqual(len(voltage_sim), len(id_sim), "Voltage and current arrays should have same length")
        self.assertTrue(np.all(id_sim >= 0), "Current values should be non-negative")

class TestDataUtils(unittest.TestCase):
    """测试data_utils模块"""
    
    def setUp(self):
        """创建测试数据"""
        self.test_data_idvg = pd.DataFrame({
            'Vgs': np.linspace(0, 10, 51),
            'Id': np.linspace(0, 20, 51) + np.random.normal(0, 0.1, 51)
        })
        
        self.test_data_idvd = pd.DataFrame({
            'Vds': np.linspace(0, 20, 41),
            'Id': np.linspace(0, 15, 41) + np.random.normal(0, 0.1, 41)
        })
    
    def test_data_validation(self):
        """测试数据验证功能"""
        # 测试正常数据
        data_utils.validate_data_consistency(self.test_data_idvg, "idvg")
        data_utils.validate_data_consistency(self.test_data_idvd, "idvd")
        
        # 测试异常数据
        bad_data = self.test_data_idvg.copy()
        bad_data.loc[10, 'Vgs'] = bad_data.loc[5, 'Vgs']  # 创建重复值
        
        with self.assertRaises(DataError):
            data_utils.validate_data_consistency(bad_data, "idvg")
    
    def test_interpolation(self):
        """测试插值功能"""
        x_source = np.array([0, 1, 2, 3, 4])
        y_source = np.array([0, 1, 4, 9, 16])
        x_target = np.array([0.5, 1.5, 2.5])
        
        y_interp = data_utils.interpolate_data(x_target, x_source, y_source)
        
        self.assertEqual(len(y_interp), len(x_target))
        self.assertAlmostEqual(y_interp[0], 0.5, places=5)  # 线性插值
    
    def test_metrics_calculation(self):
        """测试指标计算功能"""
        y_true = np.array([1, 2, 3, 4, 5])
        y_pred = np.array([1.1, 1.9, 3.1, 3.9, 5.1])
        
        rmse = data_utils.calculate_rmse(y_true, y_pred)
        r_squared = data_utils.calculate_r_squared(y_true, y_pred)
        
        self.assertGreater(rmse, 0)
        self.assertLess(rmse, 1)  # 应该是一个小的误差
        self.assertGreater(r_squared, 0.9)  # 应该有很好的拟合度
    
    def test_file_operations(self):
        """测试文件操作功能"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 测试数据保存和加载
            test_file = Path(temp_dir) / "test_data.csv"
            self.test_data_idvg.to_csv(test_file, index=False)
            
            loaded_data = data_utils.load_measurement_data(test_file, "idvg")
            pd.testing.assert_frame_equal(loaded_data, self.test_data_idvg)
            
            # 测试结果导出
            fitted_params = {'vto': 2.8, 'kp': 0.12}
            output_file = Path(temp_dir) / "test_model.lib"
            
            data_utils.export_results(fitted_params, "LEVEL=1", "idvg", output_file)
            self.assertTrue(output_file.exists())
            
            # 验证导出内容
            with open(output_file, 'r') as f:
                content = f.read()
                self.assertIn("LEVEL = 1", content)
                self.assertIn("vto", content)

class TestConfig(unittest.TestCase):
    """测试config模块"""
    
    def test_parameter_retrieval(self):
        """测试参数获取功能"""
        level1_params = config.get_model_params("LEVEL=1")
        self.assertIn('vto', level1_params)
        self.assertIn('kp', level1_params)
        
        bsim3_params = config.get_model_params("BSIM3")
        self.assertIn('vth0', bsim3_params)
        self.assertIn('u0', bsim3_params)
        
        # 测试无效模型类型
        with self.assertRaises(ValueError):
            config.get_model_params("INVALID")
    
    def test_simulation_parameters(self):
        """测试仿真参数获取"""
        idvg_params = config.get_simulation_params("idvg")
        self.assertIn('vds', idvg_params)
        self.assertIn('vgs_start', idvg_params)
        
        idvd_params = config.get_simulation_params("idvd")
        self.assertIn('vgs', idvd_params)
        self.assertIn('vds_start', idvd_params)
    
    def test_parameter_validation(self):
        """测试参数验证功能"""
        param_config = config.get_model_params("LEVEL=1")
        
        # 测试有效参数
        valid_params = {'vto': 2.5, 'kp': 0.1}
        errors = config.validate_params(valid_params, param_config)
        self.assertEqual(len(errors), 0)
        
        # 测试无效参数
        invalid_params = {'vto': 10.0, 'kp': -1.0}  # 超出范围
        errors = config.validate_params(invalid_params, param_config)
        self.assertGreater(len(errors), 0)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """准备测试数据"""
        # 创建模拟的测量数据
        self.idvg_data = pd.DataFrame({
            'Vgs': np.linspace(0, 10, 51),
            'Id': self._generate_idvg_curve(np.linspace(0, 10, 51))
        })
        
        self.idvd_data = pd.DataFrame({
            'Vds': np.linspace(0, 20, 41),
            'Id': self._generate_idvd_curve(np.linspace(0, 20, 41))
        })
    
    def _generate_idvg_curve(self, vgs, vto=2.8, kp=0.12, vds=15.0):
        """生成理想的IdVg曲线"""
        id_vals = []
        for vg in vgs:
            if vg < vto:
                id_vals.append(1e-12)  # 亚阈值电流
            else:
                # 简化的MOSFET方程
                id_val = kp * (vg - vto) * vds
                id_vals.append(id_val)
        return np.array(id_vals)
    
    def _generate_idvd_curve(self, vds, vto=2.8, kp=0.12, vgs=5.0):
        """生成理想的IdVd曲线"""
        if vgs < vto:
            return np.zeros_like(vds)
        
        id_vals = []
        for vd in vds:
            if vd < (vgs - vto):
                # 线性区
                id_val = kp * ((vgs - vto) * vd - 0.5 * vd**2)
            else:
                # 饱和区
                id_val = 0.5 * kp * (vgs - vto)**2
            id_vals.append(id_val)
        return np.array(id_vals)
    
    def test_end_to_end_fitting(self):
        """端到端拟合测试"""
        model_type = "LEVEL=1"
        
        # 设置拟合参数
        all_initial_params = {'vto': 2.5, 'kp': 0.1}
        params_to_fit = [
            {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
            {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
        ]
        
        # IdVg拟合
        idvg_template = fitting_core.get_model_template(model_type, "idvg")
        final_params_idvg, voltage_fit, id_fit = fitting_core.perform_fitting(
            idvg_template, self.idvg_data, 15.0, (0.0, 10.0, 0.2),
            params_to_fit, all_initial_params, "idvg"
        )
        
        # 验证拟合结果
        self.assertIsNotNone(final_params_idvg)
        self.assertIn('vto', final_params_idvg)
        self.assertIn('kp', final_params_idvg)
        
        # 验证拟合参数在合理范围内（由于简化模型与SPICE模型的差异，容差较大）
        self.assertGreater(final_params_idvg['vto'], 0.5)
        self.assertLess(final_params_idvg['vto'], 5.0)
        self.assertGreater(final_params_idvg['kp'], 0.01)
        self.assertLess(final_params_idvg['kp'], 1.0)

def run_all_tests():
    """运行所有测试"""
    print("=== MOSFET Fitting Tool Test Suite ===")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [TestFittingCore, TestDataUtils, TestConfig, TestIntegration]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print(f"\n=== Test Results ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\nOverall result: {'PASS' if success else 'FAIL'}")
    
    return success

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
