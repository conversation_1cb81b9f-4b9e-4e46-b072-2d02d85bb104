import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import multiprocessing
import queue
import os

import fitting_core

class FittingApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Semiconductor Device Model Fitter")
        self.geometry("1024x768")

        self.real_data = None
        self.fitted_params = None
        self.last_fit_model_type = None
        self.fitting_process = None
        self.test_line = None
        self.progress_queue = multiprocessing.Queue()

        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.iv_fitting_tab = ttk.Frame(self.notebook)
        self.export_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.iv_fitting_tab, text="IV Curve Fitting")
        self.notebook.add(self.export_tab, text="Model Export & Report")

        self.create_iv_fitting_tab()
        self.create_export_tab()

        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W).pack(side=tk.BOTTOM, fill=tk.X)
        
        self.after_id = self.after(100, self.process_queue)
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_iv_fitting_tab(self):
        pane = ttk.PanedWindow(self.iv_fitting_tab, orient=tk.HORIZONTAL)
        pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        control_frame = ttk.Frame(pane)
        pane.add(control_frame, weight=1)
        
        plot_frame = ttk.Frame(pane)
        pane.add(plot_frame, weight=3)

        action_frame = ttk.LabelFrame(control_frame, text="1. Load Data", padding="10")
        action_frame.pack(fill=tk.X, pady=5, padx=5)
        self.load_button = ttk.Button(action_frame, text="Load Id-Vg Data (.csv)", command=self.load_data)
        self.load_button.pack(fill=tk.X, pady=5)

        model_select_frame = ttk.LabelFrame(control_frame, text="2. Select Model", padding="10")
        model_select_frame.pack(fill=tk.X, pady=5, padx=5)
        self.model_type_var = tk.StringVar(value="BSIM3")
        model_select_combo = ttk.Combobox(model_select_frame, textvariable=self.model_type_var, values=["BSIM3", "LEVEL=1"], state="readonly")
        model_select_combo.pack(fill=tk.X)
        model_select_combo.bind("<<ComboboxSelected>>", self.on_model_select)

        sim_params_frame = ttk.LabelFrame(control_frame, text="3. Simulation Parameters", padding="10")
        sim_params_frame.pack(fill=tk.X, pady=5, padx=5)
        ttk.Label(sim_params_frame, text="Vds (V):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.vds_var = tk.StringVar(value="15.0")
        ttk.Entry(sim_params_frame, textvariable=self.vds_var).grid(row=0, column=1, sticky=tk.EW)
        ttk.Label(sim_params_frame, text="Vgs Start (V):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.vgs_start_var = tk.StringVar(value="0.0")
        ttk.Entry(sim_params_frame, textvariable=self.vgs_start_var).grid(row=1, column=1, sticky=tk.EW)
        ttk.Label(sim_params_frame, text="Vgs Stop (V):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.vgs_stop_var = tk.StringVar(value="10.0")
        ttk.Entry(sim_params_frame, textvariable=self.vgs_stop_var).grid(row=2, column=1, sticky=tk.EW)
        ttk.Label(sim_params_frame, text="Vgs Step (V):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.vgs_step_var = tk.StringVar(value="0.2")
        ttk.Entry(sim_params_frame, textvariable=self.vgs_step_var).grid(row=3, column=1, sticky=tk.EW)
        sim_params_frame.columnconfigure(1, weight=1)

        self.params_container = ttk.Frame(control_frame)
        self.params_container.pack(fill=tk.X, pady=5, padx=5)
        self.param_frames = {}
        self.param_widgets = {}
        self.create_level1_params_frame()
        self.create_bsim3_params_frame()

        fit_action_frame = ttk.LabelFrame(control_frame, text="5. Fit", padding="10")
        fit_action_frame.pack(fill=tk.X, pady=5, padx=5)
        self.test_button = ttk.Button(fit_action_frame, text="Test with Initial Values", command=self.test_initial_values, state=tk.DISABLED)
        self.test_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.fit_button = ttk.Button(fit_action_frame, text="Start Fitting", command=self.start_fitting_process, state=tk.DISABLED)
        self.fit_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        results_frame = ttk.LabelFrame(control_frame, text="Fitting Log", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)
        self.results_text = tk.Text(results_frame, height=10, state=tk.DISABLED)
        self.results_text.pack(fill=tk.BOTH, expand=True)

        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.ax.set_title("Id-Vgs Curve"); self.ax.set_xlabel("Vgs (V)"); self.ax.set_ylabel("Id (A)"); self.ax.grid(True)
        
        self.on_model_select(None)

    def create_param_entry(self, parent, name, default, low, high):
        frame = ttk.Frame(parent)
        var_dict = {
            'fit': tk.BooleanVar(value=True),
            'initial': tk.StringVar(value=str(default)),
            'lower': tk.StringVar(value=str(low)),
            'upper': tk.StringVar(value=str(high)),
            'frame': frame
        }
        ttk.Checkbutton(frame, variable=var_dict['fit'], text=f"{name:<6}").pack(side=tk.LEFT, fill=tk.X, padx=5)
        ttk.Label(frame, text="Initial:").pack(side=tk.LEFT, padx=(10,2))
        ttk.Entry(frame, textvariable=var_dict['initial'], width=10).pack(side=tk.LEFT)
        return var_dict

    def create_level1_params_frame(self):
        frame = ttk.LabelFrame(self.params_container, text="4. Set LEVEL=1 Parameters", padding="10")
        widgets = {}
        widgets['vto'] = self.create_param_entry(frame, "VTO", 2.5, 1.0, 4.0)
        widgets['kp'] = self.create_param_entry(frame, "KP", 0.1, 0.01, 0.5)
        for w in widgets.values(): w['frame'].pack(fill=tk.X, expand=True, pady=2)
        self.param_frames["LEVEL=1"] = frame
        self.param_widgets["LEVEL=1"] = widgets

    def create_bsim3_params_frame(self):
        frame = ttk.LabelFrame(self.params_container, text="4. Set BSIM3 Parameters", padding="10")
        widgets = {}
        widgets['vth0'] = self.create_param_entry(frame, "VTH0", 2.8, 1.0, 5.0)
        widgets['u0'] = self.create_param_entry(frame, "U0", 0.06, 0.01, 0.1)
        widgets['vsat'] = self.create_param_entry(frame, "VSAT", 1e5, 1e4, 5e5)
        widgets['k1'] = self.create_param_entry(frame, "K1", 0.5, 0.1, 2.0)
        widgets['k2'] = self.create_param_entry(frame, "K2", 0.0, -0.1, 0.1)
        widgets['eta0'] = self.create_param_entry(frame, "ETA0", 0.08, 0, 0.5)
        widgets['rdsw'] = self.create_param_entry(frame, "RDSW", 100, 0, 1000)
        for w in widgets.values(): w['frame'].pack(fill=tk.X, expand=True, pady=2)
        self.param_frames["BSIM3"] = frame
        self.param_widgets["BSIM3"] = widgets

    def on_model_select(self, event):
        model_type = self.model_type_var.get()
        for name, frame in self.param_frames.items():
            frame.pack_forget()
        self.param_frames[model_type].pack(fill=tk.X)

    def create_export_tab(self):
        export_frame = ttk.LabelFrame(self.export_tab, text="Export Fitted SPICE Model", padding="10")
        export_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.model_text = tk.Text(export_frame, height=15, font=("Courier", 10), wrap=tk.WORD)
        self.model_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.model_text.insert(tk.END, "# Fit a model in the 'IV Curve Fitting' tab first.")
        self.model_text.config(state=tk.DISABLED)
        self.export_button = ttk.Button(export_frame, text="Export to .lib File", command=self.export_model, state=tk.DISABLED)
        self.export_button.pack(pady=10)

    def load_data(self):
        file_path = filedialog.askopenfilename(title="Select Id-Vg Data File", filetypes=[("CSV Files", "*.csv")])
        if not file_path: return
        try:
            self.real_data = pd.read_csv(file_path)
            if not all(col in self.real_data.columns for col in ['Vgs', 'Id']):
                raise ValueError("CSV must contain 'Vgs' and 'Id' columns.")
            self.ax.clear()
            self.ax.plot(self.real_data['Vgs'], self.real_data['Id'], 'bo', markersize=4, label='Real Data')
            self.ax.set_title("Id-Vgs Curve"); self.ax.set_xlabel("Vgs (V)"); self.ax.set_ylabel("Id (A)"); self.ax.grid(True); self.ax.legend()
            self.canvas.draw()
            self.fit_button.config(state=tk.NORMAL)
            self.test_button.config(state=tk.NORMAL)
            self.status_var.set(f"Loaded {os.path.basename(file_path)}")
            self.clear_results()
            self.export_button.config(state=tk.DISABLED)
        except Exception as e:
            messagebox.showerror("Error Loading File", str(e))
            self.real_data = None
            self.fit_button.config(state=tk.DISABLED)
            self.test_button.config(state=tk.DISABLED)

    def get_current_params_from_gui(self):
        model_type = self.model_type_var.get()
        all_params = {}
        params_to_fit = []
        
        for name, widgets in self.param_widgets[model_type].items():
            initial_val = float(widgets['initial'].get())
            all_params[name] = initial_val
            if widgets['fit'].get():
                params_to_fit.append({
                    'name': name,
                    'initial': initial_val,
                    'lower': float(widgets['lower'].get()),
                    'upper': float(widgets['upper'].get())
                })
        
        vds = float(self.vds_var.get())
        vgs_params = (float(self.vgs_start_var.get()), float(self.vgs_stop_var.get()), float(self.vgs_step_var.get()))
        
        return model_type, all_params, params_to_fit, vds, vgs_params

    def test_initial_values(self):
        if self.real_data is None: return
        try:
            model_type, all_params, _, vds, vgs_params = self.get_current_params_from_gui()
        except ValueError:
            messagebox.showerror("Invalid Parameters", "Please ensure all parameter values are valid numbers.")
            return

        model_template = fitting_core.BSIM3_TEMPLATE if model_type == "BSIM3" else fitting_core.LEVEL1_TEMPLATE
        vgs_sim, id_sim = fitting_core.run_spice_simulation(model_template, all_params, vgs_params, vds)

        if self.test_line:
            try: self.test_line.pop(0).remove()
            except: pass

        if id_sim.size > 0:
            self.test_line = self.ax.plot(vgs_sim, id_sim, 'g--', linewidth=2, label='Test Curve')
            self.ax.legend()
            self.canvas.draw()
            self.status_var.set("Test simulation complete.")
        else:
            self.status_var.set("Test simulation failed. Check console for errors.")

    def start_fitting_process(self):
        if self.real_data is None: return
        try:
            model_type, all_params, params_to_fit, vds, vgs_params = self.get_current_params_from_gui()
            
            # --- Input Validation ---
            for p in params_to_fit:
                if not (p['lower'] <= p['initial'] <= p['upper']):
                    messagebox.showerror(
                        "Validation Error",
                        f"The initial value for parameter '{p['name']}' is outside its bounds.\n\n"
                        f"Initial: {p['initial']}\n"
                        f"Bounds: [{p['lower']}, {p['upper']}]"
                    )
                    return
            
            if not params_to_fit:
                messagebox.showwarning("No Parameters", "Please select at least one parameter to fit.")
                return

        except ValueError:
            messagebox.showerror("Invalid Parameters", "Please ensure all parameter values are valid numbers.")
            return

        self.fit_button.config(state=tk.DISABLED); self.load_button.config(state=tk.DISABLED); self.test_button.config(state=tk.DISABLED)
        self.status_var.set(f"Fitting {model_type} model...")
        self.clear_results(); self.update_results(f"Starting {model_type} fitting...\n")
        self.last_fit_model_type = model_type

        model_template = fitting_core.BSIM3_TEMPLATE if model_type == "BSIM3" else fitting_core.LEVEL1_TEMPLATE
        
        self.fitting_process = multiprocessing.Process(
            target=fitting_core.perform_fitting,
            args=(model_template, self.real_data, vds, vgs_params, params_to_fit, all_params, self.progress_queue),
            daemon=True
        )
        self.fitting_process.start()

    def process_queue(self):
        try:
            while True:
                message_type, data = self.progress_queue.get_nowait()
                if message_type == "progress":
                    params, error = data
                    log_str = ", ".join([f"{k}={v:.3e}" for k, v in params.items()])
                    self.update_results(f"Trying: {log_str}, Error={error:.2e}\n")
                elif message_type == "complete":
                    self.handle_fitting_completion(data)
        except queue.Empty:
            pass
        finally:
            self.after_id = self.after(100, self.process_queue)

    def handle_fitting_completion(self, data):
        self.fitting_process = None
        self.fit_button.config(state=tk.NORMAL); self.load_button.config(state=tk.NORMAL); self.test_button.config(state=tk.NORMAL)
        if data:
            self.fitted_params, vgs_fit, id_fit = data
            self.ax.plot(vgs_fit, id_fit, 'r-', linewidth=2, label=f'Fitted {self.last_fit_model_type} Model')
            self.ax.legend(); self.canvas.draw()
            self.status_var.set("Fitting complete.")
            self.update_results("\n--- Fitting Complete ---\n")
            for name, val in self.fitted_params.items(): self.update_results(f"Final {name}: {val:.4f}\n")
            self.update_export_tab()
            self.export_button.config(state=tk.NORMAL)
        else:
            self.status_var.set("Fitting failed or was cancelled.")
            self.update_results("\n--- Fitting Failed/Cancelled ---\n")

    def update_export_tab(self):
        if self.fitted_params is None: return
        model_type = self.last_fit_model_type
        params_str = "\n".join([f"+ {name:<10} = {val:.6g}" for name, val in self.fitted_params.items()])
        subckt_content = f"""\n.SUBCKT {model_type}_Fitted D G S B\n    .MODEL MyMOS NMOS (\n    + LEVEL = {8 if model_type == "BSIM3" else 1}\n{params_str}\n    )\n    M1 D G S B MyMOS L=1u W=10u\n.ENDS {model_type}_Fitted"""
        self.model_text.config(state=tk.NORMAL)
        self.model_text.delete('1.0', tk.END)
        self.model_text.insert(tk.END, subckt_content)
        self.model_text.config(state=tk.DISABLED)

    def export_model(self):
        if not self.fitted_params: return
        file_path = filedialog.asksaveasfilename(title="Save SPICE Model", defaultextension=".lib", filetypes=[("Library Files", "*.lib")])
        if not file_path: return
        try:
            with open(file_path, 'w') as f: f.write(self.model_text.get("1.0", tk.END))
            self.status_var.set(f"Model exported to {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to save file: {e}")

    def update_results(self, text):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.insert(tk.END, text)
        self.results_text.see(tk.END)
        self.results_text.config(state=tk.DISABLED)

    def clear_results(self):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete('1.0', tk.END)
        self.results_text.config(state=tk.DISABLED)

    def on_closing(self):
        if self.fitting_process and self.fitting_process.is_alive():
            self.fitting_process.terminate(); self.fitting_process.join()
        if self.after_id: self.after_cancel(self.after_id)
        self.destroy()

if __name__ == "__main__":
    multiprocessing.freeze_support()
    app = FittingApp()
    app.mainloop()
