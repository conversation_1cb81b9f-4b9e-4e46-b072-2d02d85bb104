import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import multiprocessing
import queue
import os
import sys
import platform

import fitting_core

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class FittingApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("MOSFET Parameter Fitting Tool")
        self.geometry("1024x768")

        # 设置字体以支持中文显示
        self.setup_fonts()

        self.real_data = None
        self.fitted_params = None
        self.last_fit_model_type = None
        self.fitting_process = None
        self.test_line = None
        self.progress_queue = multiprocessing.Queue()

        self.notebook = ttk.Notebook(self)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.iv_fitting_tab = ttk.Frame(self.notebook)
        self.export_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.iv_fitting_tab, text="IV Curve Fitting")
        self.notebook.add(self.export_tab, text="Model Export & Report")

        self.create_iv_fitting_tab()
        self.create_export_tab()

        self.status_var = tk.StringVar(value="Ready")
        ttk.Label(self, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W).pack(side=tk.BOTTOM, fill=tk.X)
        
        self.after_id = self.after(100, self.process_queue)
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_iv_fitting_tab(self):
        pane = ttk.PanedWindow(self.iv_fitting_tab, orient=tk.HORIZONTAL)
        pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        control_frame = ttk.Frame(pane)
        pane.add(control_frame, weight=1)
        
        plot_frame = ttk.Frame(pane)
        pane.add(plot_frame, weight=3)

        action_frame = ttk.LabelFrame(control_frame, text="1. Load Measurement Data", padding="10")
        action_frame.pack(fill=tk.X, pady=5, padx=5)
        self.load_button = ttk.Button(action_frame, text="Load Id-Vg Data (.csv)", command=self.load_data)
        self.load_button.pack(fill=tk.X, pady=5)

        # Data information display
        self.data_info_label = ttk.Label(action_frame, text="No data loaded", font=self.fonts['small'], foreground="gray")
        self.data_info_label.pack(pady=(2,0))

        # Fitting type selection - more prominent position
        fitting_type_frame = ttk.LabelFrame(control_frame, text="2. Fitting Mode Selection", padding="10")
        fitting_type_frame.pack(fill=tk.X, pady=5, padx=5)

        # Use radio buttons for clearer selection
        self.fitting_type_var = tk.StringVar(value="idvg")

        # IdVg option
        idvg_frame = ttk.Frame(fitting_type_frame)
        idvg_frame.pack(fill=tk.X, pady=2)
        self.idvg_radio = ttk.Radiobutton(idvg_frame, text="Id-Vg Fitting", variable=self.fitting_type_var,
                                         value="idvg", command=self.on_fitting_type_select)
        self.idvg_radio.pack(side=tk.LEFT)
        ttk.Label(idvg_frame, text="(Transfer characteristics: fixed Vds, sweep Vgs)",
                 font=self.fonts['small'], foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        # IdVd option
        idvd_frame = ttk.Frame(fitting_type_frame)
        idvd_frame.pack(fill=tk.X, pady=2)
        self.idvd_radio = ttk.Radiobutton(idvd_frame, text="Id-Vd Fitting", variable=self.fitting_type_var,
                                         value="idvd", command=self.on_fitting_type_select)
        self.idvd_radio.pack(side=tk.LEFT)
        ttk.Label(idvd_frame, text="(Output characteristics: fixed Vgs, sweep Vds)",
                 font=self.fonts['small'], foreground="gray").pack(side=tk.LEFT, padx=(10,0))

        # Current mode indicator
        self.mode_indicator = ttk.Label(fitting_type_frame, text="Current Mode: Id-Vg",
                                       font=self.fonts['title'], foreground="blue")
        self.mode_indicator.pack(pady=(5,0))

        # Quick switch buttons
        quick_switch_frame = ttk.Frame(fitting_type_frame)
        quick_switch_frame.pack(pady=(5,0))

        ttk.Button(quick_switch_frame, text="Switch to Id-Vd",
                  command=lambda: self.quick_switch_mode("idvd")).pack(side=tk.LEFT, padx=(0,5))
        ttk.Button(quick_switch_frame, text="Switch to Id-Vg",
                  command=lambda: self.quick_switch_mode("idvg")).pack(side=tk.LEFT)

        # Model type selection
        model_select_frame = ttk.LabelFrame(control_frame, text="3. SPICE Model Type", padding="10")
        model_select_frame.pack(fill=tk.X, pady=5, padx=5)

        ttk.Label(model_select_frame, text="Model:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.model_type_var = tk.StringVar(value="LEVEL=1")
        model_select_combo = ttk.Combobox(model_select_frame, textvariable=self.model_type_var,
                                         values=["LEVEL=1", "BSIM3"], state="readonly", width=15)
        model_select_combo.grid(row=0, column=1, sticky=tk.W, padx=(5,0))
        model_select_combo.bind("<<ComboboxSelected>>", self.on_model_select)

        # Model description
        model_info = ttk.Label(model_select_frame, text="LEVEL=1: Simple model, fast fitting\nBSIM3: Advanced model, more parameters",
                              font=self.fonts['small'], foreground="gray")
        model_info.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5,0))

        model_select_frame.columnconfigure(1, weight=1)

        self.sim_params_frame = ttk.LabelFrame(control_frame, text="4. Simulation Parameters", padding="10")
        self.sim_params_frame.pack(fill=tk.X, pady=5, padx=5)

        # Create containers for different parameter sets
        self.idvg_params_frame = ttk.Frame(self.sim_params_frame)
        self.idvd_params_frame = ttk.Frame(self.sim_params_frame)

        # IdVg parameters with better layout
        idvg_title = ttk.Label(self.idvg_params_frame, text="Id-Vg Parameters", font=self.fonts['title'])
        idvg_title.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0,5))

        ttk.Label(self.idvg_params_frame, text="Fixed Vds (V):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.vds_var = tk.StringVar(value="15.0")
        vds_entry = ttk.Entry(self.idvg_params_frame, textvariable=self.vds_var, width=8)
        vds_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,0))
        ttk.Label(self.idvg_params_frame, text="(drain voltage)", font=self.fonts['small'], foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=(5,0))

        ttk.Label(self.idvg_params_frame, text="Vgs Range:").grid(row=2, column=0, sticky=tk.W, pady=2)
        vgs_frame = ttk.Frame(self.idvg_params_frame)
        vgs_frame.grid(row=2, column=1, columnspan=2, sticky=tk.W, padx=(5,0))

        self.vgs_start_var = tk.StringVar(value="0.0")
        self.vgs_stop_var = tk.StringVar(value="10.0")
        self.vgs_step_var = tk.StringVar(value="0.2")

        ttk.Entry(vgs_frame, textvariable=self.vgs_start_var, width=6).pack(side=tk.LEFT)
        ttk.Label(vgs_frame, text="to").pack(side=tk.LEFT, padx=2)
        ttk.Entry(vgs_frame, textvariable=self.vgs_stop_var, width=6).pack(side=tk.LEFT)
        ttk.Label(vgs_frame, text="step").pack(side=tk.LEFT, padx=2)
        ttk.Entry(vgs_frame, textvariable=self.vgs_step_var, width=6).pack(side=tk.LEFT)

        # IdVd parameters with better layout
        idvd_title = ttk.Label(self.idvd_params_frame, text="Id-Vd Parameters", font=self.fonts['title'])
        idvd_title.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0,5))

        ttk.Label(self.idvd_params_frame, text="Fixed Vgs (V):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.vgs_fixed_var = tk.StringVar(value="5.0")
        vgs_fixed_entry = ttk.Entry(self.idvd_params_frame, textvariable=self.vgs_fixed_var, width=8)
        vgs_fixed_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,0))
        ttk.Label(self.idvd_params_frame, text="(gate voltage)", font=self.fonts['small'], foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=(5,0))

        ttk.Label(self.idvd_params_frame, text="Vds Range:").grid(row=2, column=0, sticky=tk.W, pady=2)
        vds_frame = ttk.Frame(self.idvd_params_frame)
        vds_frame.grid(row=2, column=1, columnspan=2, sticky=tk.W, padx=(5,0))

        self.vds_start_var = tk.StringVar(value="0.0")
        self.vds_stop_var = tk.StringVar(value="20.0")
        self.vds_step_var = tk.StringVar(value="0.5")

        ttk.Entry(vds_frame, textvariable=self.vds_start_var, width=6).pack(side=tk.LEFT)
        ttk.Label(vds_frame, text="to").pack(side=tk.LEFT, padx=2)
        ttk.Entry(vds_frame, textvariable=self.vds_stop_var, width=6).pack(side=tk.LEFT)
        ttk.Label(vds_frame, text="step").pack(side=tk.LEFT, padx=2)
        ttk.Entry(vds_frame, textvariable=self.vds_step_var, width=6).pack(side=tk.LEFT)

        self.params_container = ttk.Frame(control_frame)
        self.params_container.pack(fill=tk.X, pady=5, padx=5)
        self.param_frames = {}
        self.param_widgets = {}
        self.create_level1_params_frame()
        self.create_bsim3_params_frame()

        fit_action_frame = ttk.LabelFrame(control_frame, text="6. Execute Fitting", padding="10")
        fit_action_frame.pack(fill=tk.X, pady=5, padx=5)
        self.test_button = ttk.Button(fit_action_frame, text="Test with Initial Values", command=self.test_initial_values, state=tk.DISABLED)
        self.test_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        self.fit_button = ttk.Button(fit_action_frame, text="Start Fitting", command=self.start_fitting_process, state=tk.DISABLED)
        self.fit_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        results_frame = ttk.LabelFrame(control_frame, text="Fitting Log", padding="10")
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5, padx=5)
        self.results_text = tk.Text(results_frame, height=10, state=tk.DISABLED)
        self.results_text.pack(fill=tk.BOTH, expand=True)

        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        # 初始化图表，标题和标签将在on_fitting_type_select中设置
        self.ax.set_ylabel("Id (A)")
        self.ax.grid(True)

        self.on_model_select(None)
        self.on_fitting_type_select(None)  # 这会设置正确的标题和标签

    def setup_fonts(self):
        """设置字体以支持中文显示"""
        try:
            # 检测操作系统并设置合适的字体
            system = platform.system()

            if system == "Windows":
                # Windows系统字体
                default_font = ("Microsoft YaHei", 9)
                title_font = ("Microsoft YaHei", 10, "bold")
                small_font = ("Microsoft YaHei", 8)
            elif system == "Darwin":  # macOS
                # macOS系统字体
                default_font = ("PingFang SC", 9)
                title_font = ("PingFang SC", 10, "bold")
                small_font = ("PingFang SC", 8)
            else:  # Linux
                # Linux系统字体
                default_font = ("DejaVu Sans", 9)
                title_font = ("DejaVu Sans", 10, "bold")
                small_font = ("DejaVu Sans", 8)

            # 设置tkinter默认字体
            self.option_add("*Font", default_font)
            self.option_add("*Label.Font", default_font)
            self.option_add("*Button.Font", default_font)
            self.option_add("*Entry.Font", default_font)

            # 存储字体配置供后续使用
            self.fonts = {
                'default': default_font,
                'title': title_font,
                'small': small_font
            }

        except Exception as e:
            print(f"Font setup warning: {e}")
            # 如果字体设置失败，使用默认字体
            self.fonts = {
                'default': ("Arial", 9),
                'title': ("Arial", 10, "bold"),
                'small': ("Arial", 8)
            }

    def create_param_entry(self, parent, name, default, low, high):
        frame = ttk.Frame(parent)
        var_dict = {
            'fit': tk.BooleanVar(value=True),
            'initial': tk.StringVar(value=str(default)),
            'lower': tk.StringVar(value=str(low)),
            'upper': tk.StringVar(value=str(high)),
            'frame': frame
        }
        ttk.Checkbutton(frame, variable=var_dict['fit'], text=f"{name:<6}").pack(side=tk.LEFT, fill=tk.X, padx=5)
        ttk.Label(frame, text="Initial:").pack(side=tk.LEFT, padx=(10,2))
        ttk.Entry(frame, textvariable=var_dict['initial'], width=10).pack(side=tk.LEFT)
        return var_dict

    def create_level1_params_frame(self):
        frame = ttk.LabelFrame(self.params_container, text="5. LEVEL=1 Model Parameters", padding="10")
        widgets = {}
        widgets['vto'] = self.create_param_entry(frame, "VTO", 2.5, 1.0, 4.0)
        widgets['kp'] = self.create_param_entry(frame, "KP", 0.1, 0.01, 0.5)
        for w in widgets.values(): w['frame'].pack(fill=tk.X, expand=True, pady=2)
        self.param_frames["LEVEL=1"] = frame
        self.param_widgets["LEVEL=1"] = widgets

    def create_bsim3_params_frame(self):
        frame = ttk.LabelFrame(self.params_container, text="5. BSIM3 Model Parameters", padding="10")
        widgets = {}
        widgets['vth0'] = self.create_param_entry(frame, "VTH0", 2.8, 1.0, 5.0)
        widgets['u0'] = self.create_param_entry(frame, "U0", 0.06, 0.01, 0.1)
        widgets['vsat'] = self.create_param_entry(frame, "VSAT", 1e5, 1e4, 5e5)
        widgets['k1'] = self.create_param_entry(frame, "K1", 0.5, 0.1, 2.0)
        widgets['k2'] = self.create_param_entry(frame, "K2", 0.0, -0.1, 0.1)
        widgets['eta0'] = self.create_param_entry(frame, "ETA0", 0.08, 0, 0.5)
        widgets['rdsw'] = self.create_param_entry(frame, "RDSW", 100, 0, 1000)
        for w in widgets.values(): w['frame'].pack(fill=tk.X, expand=True, pady=2)
        self.param_frames["BSIM3"] = frame
        self.param_widgets["BSIM3"] = widgets

    def on_model_select(self, event):
        model_type = self.model_type_var.get()
        for name, frame in self.param_frames.items():
            frame.pack_forget()
        self.param_frames[model_type].pack(fill=tk.X)

        # BSIM3特殊提示
        if model_type == "BSIM3":
            self.show_bsim3_usage_tip()

    def show_bsim3_usage_tip(self):
        """Show BSIM3 usage tips"""
        tip_msg = """BSIM3 Model Usage Recommendations:

Data File Selection:
• IdVg fitting: Use real_data_bsim3_idvg.csv
• IdVd fitting: Use real_data_bsim3_idvd.csv

Parameter Fitting Strategy:
• Beginners: Select only VTH0 parameter
• Advanced users: Select VTH0 + U0 parameters
• Avoid fitting more than 3 parameters simultaneously

Important Notes:
• BSIM3 fitting takes longer time, please be patient
• Use smaller parameter boundary ranges
• If fitting fails, reduce the number of parameters

Do you need to generate BSIM3 specific data?"""

        response = messagebox.askyesno("BSIM3 Usage Tips", tip_msg)
        if response:
            self.generate_bsim3_data()

    def generate_bsim3_data(self):
        """Generate BSIM3 data"""
        try:
            import subprocess
            result = subprocess.run(['uv', 'run', 'python', '3_generate_bsim3_data.py'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                messagebox.showinfo("Success", "BSIM3 data generation completed!\n\nGenerated files:\n• real_data_bsim3_idvg.csv\n• real_data_bsim3_idvd.csv")
            else:
                messagebox.showerror("Error", f"Data generation failed:\n{result.stderr}")
        except Exception as e:
            messagebox.showerror("Error", f"Cannot generate BSIM3 data:\n{e}")

    def on_fitting_type_select(self, event=None):
        fitting_type = self.fitting_type_var.get()

        # Hide all parameter frames
        self.idvg_params_frame.pack_forget()
        self.idvd_params_frame.pack_forget()

        # Show the appropriate parameter frame and update UI
        if fitting_type == "idvg":
            self.idvg_params_frame.pack(fill=tk.X, pady=5)
            self.ax.clear()
            self.ax.set_title("Id-Vgs Transfer Characteristics", fontsize=12, fontweight='bold')
            self.ax.set_xlabel("Gate Voltage Vgs (V)")
            self.ax.set_ylabel("Drain Current Id (A)")
            self.mode_indicator.config(text="Current Mode: Id-Vg (Transfer)", foreground="blue")

            # Update load button text
            if hasattr(self, 'load_button'):
                self.load_button.config(text="Load Id-Vg Data (.csv)")

        elif fitting_type == "idvd":
            self.idvd_params_frame.pack(fill=tk.X, pady=5)
            self.ax.clear()
            self.ax.set_title("Id-Vds Output Characteristics", fontsize=12, fontweight='bold')
            self.ax.set_xlabel("Drain Voltage Vds (V)")
            self.ax.set_ylabel("Drain Current Id (A)")
            self.mode_indicator.config(text="Current Mode: Id-Vd (Output)", foreground="red")

            # Update load button text
            if hasattr(self, 'load_button'):
                self.load_button.config(text="Load Id-Vd Data (.csv)")

        # 设置网格和样式
        self.ax.grid(True, alpha=0.3)
        self.ax.set_facecolor('#f8f9fa')
        self.canvas.draw()

        # 清除之前的数据和结果
        self.real_data = None
        self.fitted_params = None
        self.fit_button.config(state=tk.DISABLED)
        self.test_button.config(state=tk.DISABLED)
        if hasattr(self, 'export_button'):
            self.export_button.config(state=tk.DISABLED)
        self.clear_results()

        print(f"Switched to {fitting_type.upper()} fitting mode")

    def quick_switch_mode(self, mode):
        """快捷切换拟合模式"""
        if self.fitting_type_var.get() != mode:
            self.fitting_type_var.set(mode)
            self.on_fitting_type_select()

            # 显示切换提示
            mode_name = "Id-Vg Transfer" if mode == "idvg" else "Id-Vd Output"
            self.status_var.set(f"Switched to {mode_name} fitting mode")

            # 如果有数据，提醒用户重新加载
            if self.real_data is not None:
                response = messagebox.askyesno(
                    "Mode Switched",
                    f"You've switched to {mode_name} mode.\n\n"
                    f"The current data may not be compatible.\n"
                    f"Would you like to load new data now?",
                    icon="question"
                )
                if response:
                    self.load_data()

    def create_export_tab(self):
        export_frame = ttk.LabelFrame(self.export_tab, text="Export Fitted SPICE Model", padding="10")
        export_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.model_text = tk.Text(export_frame, height=15, font=("Courier", 10), wrap=tk.WORD)
        self.model_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.model_text.insert(tk.END, "# Fit a model in the 'IV Curve Fitting' tab first.")
        self.model_text.config(state=tk.DISABLED)
        self.export_button = ttk.Button(export_frame, text="Export to .lib File", command=self.export_model, state=tk.DISABLED)
        self.export_button.pack(pady=10)

    def load_data(self):
        fitting_type = self.fitting_type_var.get()
        if fitting_type == "idvg":
            title = "Select Id-Vg Data File"
            required_cols = ['Vgs', 'Id']
        else:  # idvd
            title = "Select Id-Vd Data File"
            required_cols = ['Vds', 'Id']

        file_path = filedialog.askopenfilename(title=title, filetypes=[("CSV Files", "*.csv")])
        if not file_path: return
        try:
            self.real_data = pd.read_csv(file_path)
            if not all(col in self.real_data.columns for col in required_cols):
                raise ValueError(f"CSV must contain {required_cols} columns.")

            self.ax.clear()
            if fitting_type == "idvg":
                self.ax.plot(self.real_data['Vgs'], self.real_data['Id'], 'bo', markersize=4, label='Real Data')
                self.ax.set_title("Id-Vgs Curve")
                self.ax.set_xlabel("Vgs (V)")
            else:  # idvd
                self.ax.plot(self.real_data['Vds'], self.real_data['Id'], 'bo', markersize=4, label='Real Data')
                self.ax.set_title("Id-Vds Curve")
                self.ax.set_xlabel("Vds (V)")

            self.ax.set_ylabel("Drain Current Id (A)")
            self.ax.grid(True, alpha=0.3)
            self.ax.legend()
            self.canvas.draw()

            # Update data information
            data_points = len(self.real_data)
            voltage_range = f"{self.real_data.iloc[:, 0].min():.1f} to {self.real_data.iloc[:, 0].max():.1f} V"
            current_range = f"{self.real_data['Id'].min():.3f} to {self.real_data['Id'].max():.3f} A"
            self.data_info_label.config(
                text=f"OK: {data_points} points | Voltage: {voltage_range} | Current: {current_range}",
                foreground="green"
            )

            self.fit_button.config(state=tk.NORMAL)
            self.test_button.config(state=tk.NORMAL)
            self.status_var.set(f"Loaded {os.path.basename(file_path)} - {data_points} data points")
            self.clear_results()
            if hasattr(self, 'export_button'):
                self.export_button.config(state=tk.DISABLED)
        except Exception as e:
            messagebox.showerror("Error Loading File", str(e))
            self.real_data = None
            self.data_info_label.config(text="ERROR: Failed to load data", foreground="red")
            self.fit_button.config(state=tk.DISABLED)
            self.test_button.config(state=tk.DISABLED)

    def get_current_params_from_gui(self):
        model_type = self.model_type_var.get()
        fitting_type = self.fitting_type_var.get()
        all_params = {}
        params_to_fit = []

        for name, widgets in self.param_widgets[model_type].items():
            initial_val = float(widgets['initial'].get())
            all_params[name] = initial_val
            if widgets['fit'].get():
                params_to_fit.append({
                    'name': name,
                    'initial': initial_val,
                    'lower': float(widgets['lower'].get()),
                    'upper': float(widgets['upper'].get())
                })

        if fitting_type == "idvg":
            fixed_voltage = float(self.vds_var.get())
            sweep_params = (float(self.vgs_start_var.get()), float(self.vgs_stop_var.get()), float(self.vgs_step_var.get()))
        else:  # idvd
            fixed_voltage = float(self.vgs_fixed_var.get())
            sweep_params = (float(self.vds_start_var.get()), float(self.vds_stop_var.get()), float(self.vds_step_var.get()))

        return model_type, fitting_type, all_params, params_to_fit, fixed_voltage, sweep_params

    def test_initial_values(self):
        if self.real_data is None: return
        try:
            model_type, fitting_type, all_params, _, fixed_voltage, sweep_params = self.get_current_params_from_gui()
        except ValueError:
            messagebox.showerror("Invalid Parameters", "Please ensure all parameter values are valid numbers.")
            return

        model_template = fitting_core.get_model_template(model_type, fitting_type)
        voltage_sim, id_sim = fitting_core.run_spice_simulation(model_template, all_params, sweep_params, fixed_voltage, fitting_type)

        if self.test_line:
            try: self.test_line.pop(0).remove()
            except: pass

        if id_sim.size > 0:
            self.test_line = self.ax.plot(voltage_sim, id_sim, 'g--', linewidth=2, label='Test Curve')
            self.ax.legend()
            self.canvas.draw()
            self.status_var.set("Test simulation complete.")
        else:
            self.status_var.set("Test simulation failed. Check console for errors.")

    def start_fitting_process(self):
        if self.real_data is None: return
        try:
            model_type, fitting_type, all_params, params_to_fit, fixed_voltage, sweep_params = self.get_current_params_from_gui()

            # --- Input Validation ---
            for p in params_to_fit:
                if not (p['lower'] <= p['initial'] <= p['upper']):
                    messagebox.showerror(
                        "Validation Error",
                        f"The initial value for parameter '{p['name']}' is outside its bounds.\n\n"
                        f"Initial: {p['initial']}\n"
                        f"Bounds: [{p['lower']}, {p['upper']}]"
                    )
                    return

            if not params_to_fit:
                messagebox.showwarning("No Parameters", "Please select at least one parameter to fit.")
                return

        except ValueError:
            messagebox.showerror("Invalid Parameters", "Please ensure all parameter values are valid numbers.")
            return

        self.fit_button.config(state=tk.DISABLED); self.load_button.config(state=tk.DISABLED); self.test_button.config(state=tk.DISABLED)
        self.status_var.set(f"Fitting {model_type} model ({fitting_type})...")
        self.clear_results(); self.update_results(f"Starting {model_type} {fitting_type} fitting...\n")
        self.last_fit_model_type = model_type
        self.last_fit_type = fitting_type

        model_template = fitting_core.get_model_template(model_type, fitting_type)

        self.fitting_process = multiprocessing.Process(
            target=fitting_core.perform_fitting,
            args=(model_template, self.real_data, fixed_voltage, sweep_params, params_to_fit, all_params, fitting_type, self.progress_queue),
            daemon=True
        )
        self.fitting_process.start()

    def process_queue(self):
        try:
            while True:
                message_type, data = self.progress_queue.get_nowait()
                if message_type == "progress":
                    params, error = data
                    log_str = ", ".join([f"{k}={v:.3e}" for k, v in params.items()])
                    self.update_results(f"Trying: {log_str}, Error={error:.2e}\n")
                elif message_type == "complete":
                    self.handle_fitting_completion(data)
        except queue.Empty:
            pass
        finally:
            self.after_id = self.after(100, self.process_queue)

    def handle_fitting_completion(self, data):
        self.fitting_process = None
        self.fit_button.config(state=tk.NORMAL); self.load_button.config(state=tk.NORMAL); self.test_button.config(state=tk.NORMAL)
        if data:
            self.fitted_params, voltage_fit, id_fit = data
            fit_label = f'Fitted {self.last_fit_model_type} Model ({self.last_fit_type})'
            self.ax.plot(voltage_fit, id_fit, 'r-', linewidth=2, label=fit_label)
            self.ax.legend(); self.canvas.draw()
            self.status_var.set("Fitting complete.")
            self.update_results("\n--- Fitting Complete ---\n")
            for name, val in self.fitted_params.items(): self.update_results(f"Final {name}: {val:.4f}\n")
            self.update_export_tab()
            self.export_button.config(state=tk.NORMAL)
        else:
            self.status_var.set("Fitting failed or was cancelled.")
            self.update_results("\n--- Fitting Failed/Cancelled ---\n")

    def update_export_tab(self):
        if self.fitted_params is None: return
        model_type = self.last_fit_model_type
        fitting_type = getattr(self, 'last_fit_type', 'idvg')
        params_str = "\n".join([f"+ {name:<10} = {val:.6g}" for name, val in self.fitted_params.items()])

        # Determine the number of terminals based on model type
        if model_type == "BSIM3":
            terminals = "D G S B"
            level = 8
        else:  # LEVEL=1
            terminals = "D G S"
            level = 1

        subckt_content = f"""\n.SUBCKT {model_type}_Fitted_{fitting_type} {terminals}\n    .MODEL MyMOS NMOS (\n    + LEVEL = {level}\n{params_str}\n    )\n    M1 {terminals} MyMOS L=1u W=10u\n.ENDS {model_type}_Fitted_{fitting_type}"""
        self.model_text.config(state=tk.NORMAL)
        self.model_text.delete('1.0', tk.END)
        self.model_text.insert(tk.END, subckt_content)
        self.model_text.config(state=tk.DISABLED)

    def export_model(self):
        if not self.fitted_params: return
        file_path = filedialog.asksaveasfilename(title="Save SPICE Model", defaultextension=".lib", filetypes=[("Library Files", "*.lib")])
        if not file_path: return
        try:
            with open(file_path, 'w') as f: f.write(self.model_text.get("1.0", tk.END))
            self.status_var.set(f"Model exported to {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to save file: {e}")

    def update_results(self, text):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.insert(tk.END, text)
        self.results_text.see(tk.END)
        self.results_text.config(state=tk.DISABLED)

    def clear_results(self):
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete('1.0', tk.END)
        self.results_text.config(state=tk.DISABLED)

    def on_closing(self):
        """安全关闭应用程序，清理所有资源"""
        try:
            # 停止拟合进程
            if hasattr(self, 'fitting_process') and self.fitting_process and self.fitting_process.is_alive():
                print("Terminating fitting process...")
                self.fitting_process.terminate()
                self.fitting_process.join(timeout=2)  # 等待最多2秒
                if self.fitting_process.is_alive():
                    print("Force killing fitting process...")
                    self.fitting_process.kill()

            # 取消定时器
            if hasattr(self, 'after_id') and self.after_id:
                self.after_cancel(self.after_id)

            # 清理matplotlib资源
            if hasattr(self, 'fig'):
                plt.close(self.fig)

            # 清理队列
            if hasattr(self, 'progress_queue'):
                try:
                    # 清空队列
                    while not self.progress_queue.empty():
                        self.progress_queue.get_nowait()
                except:
                    pass

            print("GUI resources cleaned up")

        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            # 强制退出
            self.quit()  # 退出mainloop
            self.destroy()  # 销毁窗口

            # 确保进程完全退出
            import sys
            import os
            print("Forcing application exit...")
            os._exit(0)  # 强制退出，不执行清理代码

if __name__ == "__main__":
    import signal
    import sys
    import atexit

    multiprocessing.freeze_support()

    def signal_handler(signum, frame):
        """处理系统信号"""
        print(f"Received signal {signum}, exiting...")
        sys.exit(0)

    def cleanup_on_exit():
        """程序退出时的清理函数"""
        print("Application exiting...")

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号

    # 注册退出清理函数
    atexit.register(cleanup_on_exit)

    try:
        app = FittingApp()
        print("GUI application started. Close window or press Ctrl+C to exit.")
        app.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")
    finally:
        print("Application finished")
        sys.exit(0)
