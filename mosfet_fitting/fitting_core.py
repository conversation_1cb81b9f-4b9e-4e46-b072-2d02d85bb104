import os
import subprocess
import numpy as np
from scipy.optimize import least_squares

# --- SPICE Simulation Netlist Templates ---

LEVEL1_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (LEVEL=1)
.PARAM fit_vto = {vto}
.PARAM fit_kp = {kp}
.SUBCKT FittableMOSFET D G S
    .MODEL MyMOS NMOS (
    + LEVEL = 1
    + VTO = fit_vto
    + KP = fit_kp
    )
    M1 D G S S MyMOS L=1u W=10u
.ENDS FittableMOSFET
* Test Bench
X1 D_node G_node 0 FittableMOSFET
Vds D_node 0 DC {vds}
Vgs G_node 0 DC 0
.DC Vgs {vgs_start} {vgs_stop} {vgs_step}
.control
    run
    wrdata {outfile} v(g_node) I(Vds)
.endc
.end
"""

BSIM3_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (BSIM3)

.SUBCKT FittableMOSFET D G S B
    .MODEL MyMOS NMOS (
    + LEVEL = 8
    * --- Core DC Parameters to be fitted ---
    + VTH0 = {vth0}
    + U0 = {u0}
    + VSAT = {vsat}
    + K1 = {k1}
    + K2 = {k2}
    + ETA0 = {eta0}
    + RDSW = {rdsw}
    * --- Other nominal BSIM3 parameters ---
    + LINT = 0.1u WINT = 0.1u
    + TOX = 1.5e-8
    + NCH = 1.7e17
    )
    M1 D G S B MyMOS L=1u W=10u
.ENDS FittableMOSFET

* --- Test Bench ---
* Note: BSIM3 has a 4th terminal (Body), which we tie to Source (0)
X1 D_node G_node 0 0 FittableMOSFET
Vds D_node 0 DC {vds}
Vgs G_node 0 DC 0
.DC Vgs {vgs_start} {vgs_stop} {vgs_step}

.control
    run
    wrdata {outfile} v(g_node) I(Vds)
.endc
.end
"""

# IdVd拟合模板 - LEVEL=1
LEVEL1_IDVD_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (LEVEL=1 IdVd)
.PARAM fit_vto = {vto}
.PARAM fit_kp = {kp}
.SUBCKT FittableMOSFET D G S
    .MODEL MyMOS NMOS (
    + LEVEL = 1
    + VTO = fit_vto
    + KP = fit_kp
    )
    M1 D G S S MyMOS L=1u W=10u
.ENDS FittableMOSFET
* Test Bench
X1 D_node G_node 0 FittableMOSFET
Vds D_node 0 DC 0
Vgs G_node 0 DC {vgs}
.DC Vds {vds_start} {vds_stop} {vds_step}
.control
    run
    wrdata {outfile} v(d_node) I(Vds)
.endc
.end
"""

# IdVd拟合模板 - BSIM3
BSIM3_IDVD_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (BSIM3 IdVd)

.SUBCKT FittableMOSFET D G S B
    .MODEL MyMOS NMOS (
    + LEVEL = 8
    * --- Core DC Parameters to be fitted ---
    + VTH0 = {vth0}
    + U0 = {u0}
    + VSAT = {vsat}
    + K1 = {k1}
    + K2 = {k2}
    + ETA0 = {eta0}
    + RDSW = {rdsw}
    * --- Other nominal BSIM3 parameters ---
    + LINT = 0.1u WINT = 0.1u
    + TOX = 1.5e-8
    + NCH = 1.7e17
    )
    M1 D G S B MyMOS L=1u W=10u
.ENDS FittableMOSFET

* --- Test Bench ---
* Note: BSIM3 has a 4th terminal (Body), which we tie to Source (0)
X1 D_node G_node 0 0 FittableMOSFET
Vds D_node 0 DC 0
Vgs G_node 0 DC {vgs}
.DC Vds {vds_start} {vds_stop} {vds_step}

.control
    run
    wrdata {outfile} v(d_node) I(Vds)
.endc
.end
"""

def run_spice_simulation(model_template, sim_params, sweep_params, fixed_voltage, sim_type="idvg"):
    """
    运行SPICE仿真

    Args:
        model_template: SPICE网表模板
        sim_params: 模型参数字典
        sweep_params: 扫描参数 (start, stop, step)
        fixed_voltage: 固定电压值
        sim_type: 仿真类型 "idvg" 或 "idvd"

    Returns:
        tuple: (扫描电压数组, 电流数组)
    """
    pid = os.getpid()
    sim_outfile = f"sim_data_{pid}.txt"
    cir_file = f"temp_sim_{pid}.cir"

    if sim_type == "idvg":
        # Id-Vg仿真：扫描Vgs，固定Vds
        all_params = {
            **sim_params,
            "vds": fixed_voltage,
            "vgs_start": sweep_params[0],
            "vgs_stop": sweep_params[1],
            "vgs_step": sweep_params[2],
            "outfile": sim_outfile
        }
    elif sim_type == "idvd":
        # Id-Vd仿真：扫描Vds，固定Vgs
        all_params = {
            **sim_params,
            "vgs": fixed_voltage,
            "vds_start": sweep_params[0],
            "vds_stop": sweep_params[1],
            "vds_step": sweep_params[2],
            "outfile": sim_outfile
        }
    else:
        raise ValueError(f"Unsupported simulation type: {sim_type}")

    sim_netlist = model_template.format(**all_params)

    with open(cir_file, 'w') as f:
        f.write(sim_netlist)

    try:
        # Use capture_output=True to get stdout and stderr
        result = subprocess.run(
            f"ngspice -b {cir_file}",
            shell=True, check=True, capture_output=True, text=True
        )
        sim_results = np.loadtxt(sim_outfile)
        voltage_sim = sim_results[:, 0]
        id_sim = -sim_results[:, -1]
        return voltage_sim, id_sim
    except subprocess.CalledProcessError as e:
        # This is the crucial part: print the actual error from ngspice
        print("--- NGSPICE STDERR ---")
        print(e.stderr)
        print("----------------------")
        return np.array([]), np.array([])
    except Exception as e:
        print(f"An unexpected error occurred during simulation: {e}")
        return np.array([]), np.array([])
    finally:
        if os.path.exists(sim_outfile): os.remove(sim_outfile)
        if os.path.exists(cir_file): os.remove(cir_file)

def perform_fitting(model_template, real_data_df, fixed_voltage, sweep_params, params_to_fit, all_initial_params, sim_type="idvg", progress_queue=None):
    """
    执行参数拟合

    Args:
        model_template: SPICE网表模板
        real_data_df: 真实数据DataFrame
        fixed_voltage: 固定电压值
        sweep_params: 扫描参数 (start, stop, step)
        params_to_fit: 待拟合参数列表
        all_initial_params: 所有初始参数字典
        sim_type: 仿真类型 "idvg" 或 "idvd"
        progress_queue: 进度队列

    Returns:
        tuple: (最终参数, 拟合电压数组, 拟合电流数组)
    """
    if sim_type == "idvg":
        voltage_col, current_col = 'Vgs', 'Id'
    elif sim_type == "idvd":
        voltage_col, current_col = 'Vds', 'Id'
    else:
        raise ValueError(f"Unsupported simulation type: {sim_type}")

    voltage_real = real_data_df[voltage_col].values
    id_real = real_data_df[current_col].values

    initial_guesses = [p['initial'] for p in params_to_fit]
    bounds_lower = [p['lower'] for p in params_to_fit]
    bounds_upper = [p['upper'] for p in params_to_fit]
    param_names_to_fit = [p['name'] for p in params_to_fit]

    def objective_function(current_fitted_values, voltage_real, id_real):
        # Create a full parameter dictionary for the simulation
        # Start with all initial params, then update with the current values from the optimizer
        sim_params = all_initial_params.copy()
        fitted_params_dict = {name: val for name, val in zip(param_names_to_fit, current_fitted_values)}
        sim_params.update(fitted_params_dict)

        voltage_sim, id_sim = run_spice_simulation(model_template, sim_params, sweep_params, fixed_voltage, sim_type)

        if voltage_sim.size == 0:
            return np.full_like(id_real, 1e6)

        id_sim_interp = np.interp(voltage_real, voltage_sim, id_sim)
        residuals = id_sim_interp - id_real

        if progress_queue:
            error = np.sum(residuals**2)
            progress_queue.put(("progress", (fitted_params_dict, error)))

        return residuals

    result = least_squares(
        objective_function,
        initial_guesses,
        args=(voltage_real, id_real),
        bounds=(bounds_lower, bounds_upper),
        method='trf',
        verbose=0
    )

    # Create the final dictionary of all parameters
    final_params = all_initial_params.copy()
    final_params.update({name: val for name, val in zip(param_names_to_fit, result.x)})

    # Run a final simulation with all the final parameters to get the curve
    voltage_fit, id_fit = run_spice_simulation(model_template, final_params, sweep_params, fixed_voltage, sim_type)

    if progress_queue:
        progress_queue.put(("complete", (final_params, voltage_fit, id_fit)))

    return final_params, voltage_fit, id_fit

def get_model_template(model_type, sim_type):
    """
    根据模型类型和仿真类型获取对应的SPICE模板

    Args:
        model_type: 模型类型 "LEVEL=1" 或 "BSIM3"
        sim_type: 仿真类型 "idvg" 或 "idvd"

    Returns:
        str: SPICE网表模板
    """
    template_map = {
        ("LEVEL=1", "idvg"): LEVEL1_TEMPLATE,
        ("LEVEL=1", "idvd"): LEVEL1_IDVD_TEMPLATE,
        ("BSIM3", "idvg"): BSIM3_TEMPLATE,
        ("BSIM3", "idvd"): BSIM3_IDVD_TEMPLATE,
    }

    key = (model_type, sim_type)
    if key not in template_map:
        raise ValueError(f"Unsupported combination: model_type={model_type}, sim_type={sim_type}")

    return template_map[key]