import os
import subprocess
import numpy as np
from scipy.optimize import least_squares

# --- SPICE Simulation Netlist Templates ---

LEVEL1_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (LEVEL=1)
.PARAM fit_vto = {vto}
.PARAM fit_kp = {kp}
.SUBCKT FittableMOSFET D G S
    .MODEL MyMOS NMOS (
    + LEVEL = 1
    + VTO = fit_vto
    + KP = fit_kp
    )
    M1 D G S S MyMOS L=1u W=10u
.ENDS FittableMOSFET
* Test Bench
X1 D_node G_node 0 FittableMOSFET
Vds D_node 0 DC {vds}
Vgs G_node 0 DC 0
.DC Vgs {vgs_start} {vgs_stop} {vgs_step}
.control
    run
    wrdata {outfile} v(g_node) I(Vds)
.endc
.end
"""

BSIM3_TEMPLATE = """
.title Fittable MOSFET Model Test Bench (BSIM3)

.SUBCKT FittableMOSFET D G S B
    .MODEL MyMOS NMOS (
    + LEVEL = 8
    * --- Core DC Parameters to be fitted ---
    + VTH0 = {vth0}
    + U0 = {u0}
    + VSAT = {vsat}
    + K1 = {k1}
    + K2 = {k2}
    + ETA0 = {eta0}
    + RDSW = {rdsw}
    * --- Other nominal BSIM3 parameters ---
    + LINT = 0.1u WINT = 0.1u
    + TOX = 1.5e-8
    + NCH = 1.7e17
    )
    M1 D G S B MyMOS L=1u W=10u
.ENDS FittableMOSFET

* --- Test Bench ---
* Note: BSIM3 has a 4th terminal (Body), which we tie to Source (0)
X1 D_node G_node 0 0 FittableMOSFET
Vds D_node 0 DC {vds}
Vgs G_node 0 DC 0
.DC Vgs {vgs_start} {vgs_stop} {vgs_step}

.control
    run
    wrdata {outfile} v(g_node) I(Vds)
.endc
.end
"""

def run_spice_simulation(model_template, sim_params, vgs_params, vds_voltage):
    pid = os.getpid()
    sim_outfile = f"sim_data_{pid}.txt"
    cir_file = f"temp_sim_{pid}.cir"

    all_params = {
        **sim_params,
        "vds": vds_voltage,
        "vgs_start": vgs_params[0],
        "vgs_stop": vgs_params[1],
        "vgs_step": vgs_params[2],
        "outfile": sim_outfile
    }

    sim_netlist = model_template.format(**all_params)

    with open(cir_file, 'w') as f:
        f.write(sim_netlist)

    try:
        # Use capture_output=True to get stdout and stderr
        result = subprocess.run(
            f"ngspice -b {cir_file}",
            shell=True, check=True, capture_output=True, text=True
        )
        sim_results = np.loadtxt(sim_outfile)
        vgs_sim = sim_results[:, 0]
        id_sim = -sim_results[:, -1]
        return vgs_sim, id_sim
    except subprocess.CalledProcessError as e:
        # This is the crucial part: print the actual error from ngspice
        print("--- NGSPICE STDERR ---")
        print(e.stderr)
        print("----------------------")
        return np.array([]), np.array([])
    except Exception as e:
        print(f"An unexpected error occurred during simulation: {e}")
        return np.array([]), np.array([])
    finally:
        if os.path.exists(sim_outfile): os.remove(sim_outfile)
        if os.path.exists(cir_file): os.remove(cir_file)

def perform_fitting(model_template, real_data_df, vds_voltage, vgs_params, params_to_fit, all_initial_params, progress_queue=None):
    vgs_real = real_data_df['Vgs'].values
    id_real = real_data_df['Id'].values

    initial_guesses = [p['initial'] for p in params_to_fit]
    bounds_lower = [p['lower'] for p in params_to_fit]
    bounds_upper = [p['upper'] for p in params_to_fit]
    param_names_to_fit = [p['name'] for p in params_to_fit]

    def objective_function(current_fitted_values, vgs_real, id_real):
        # Create a full parameter dictionary for the simulation
        # Start with all initial params, then update with the current values from the optimizer
        sim_params = all_initial_params.copy()
        fitted_params_dict = {name: val for name, val in zip(param_names_to_fit, current_fitted_values)}
        sim_params.update(fitted_params_dict)
        
        vgs_sim, id_sim = run_spice_simulation(model_template, sim_params, vgs_params, vds_voltage)
        
        if vgs_sim.size == 0:
            return np.full_like(id_real, 1e6)

        id_sim_interp = np.interp(vgs_real, vgs_sim, id_sim)
        residuals = id_sim_interp - id_real
        
        if progress_queue:
            error = np.sum(residuals**2)
            progress_queue.put(("progress", (fitted_params_dict, error)))
            
        return residuals

    result = least_squares(
        objective_function,
        initial_guesses,
        args=(vgs_real, id_real),
        bounds=(bounds_lower, bounds_upper),
        method='trf',
        verbose=0
    )

    # Create the final dictionary of all parameters
    final_params = all_initial_params.copy()
    final_params.update({name: val for name, val in zip(param_names_to_fit, result.x)})
    
    # Run a final simulation with all the final parameters to get the curve
    vgs_fit, id_fit = run_spice_simulation(model_template, final_params, vgs_params, vds_voltage)
    
    if progress_queue:
        progress_queue.put(("complete", (final_params, vgs_fit, id_fit)))
        
    return final_params, vgs_fit, id_fit