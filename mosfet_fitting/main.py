#!/usr/bin/env python3
"""
MOSFET参数拟合主程序
优化后的版本，展示了模块化设计和错误处理
"""

import argparse
import sys
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt

# 导入自定义模块
import fitting_core
import config
import data_utils
from exceptions import FittingError, DataError, SimulationError

def setup_argument_parser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(description='MOSFET Parameter Fitting Tool')
    parser.add_argument('--mode', choices=['idvg', 'idvd', 'joint'], default='idvg',
                       help='Fitting mode (default: idvg)')
    parser.add_argument('--model', choices=['LEVEL=1', 'BSIM3'], default='LEVEL=1',
                       help='SPICE model type (default: LEVEL=1)')
    parser.add_argument('--idvg-data', type=str, default=config.FILE_PATHS['idvg_data'],
                       help='IdVg data file path')
    parser.add_argument('--idvd-data', type=str, default=config.FILE_PATHS['idvd_data'],
                       help='IdVd data file path')
    parser.add_argument('--output', type=str, default='fitted_model.lib',
                       help='Output SPICE model file')
    parser.add_argument('--plot', action='store_true',
                       help='Generate plots of fitting results')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose output')
    return parser

def perform_single_fitting(mode, model_type, data_file, verbose=False):
    """执行单一类型的拟合"""
    try:
        # 加载数据
        if verbose:
            print(f"Loading {mode} data from {data_file}...")
        data = data_utils.load_measurement_data(data_file, mode)
        data_utils.validate_data_consistency(data, mode)

        # 获取默认参数
        model_params = config.get_model_params(model_type)
        sim_params = config.get_simulation_params(mode)

        # 设置拟合参数
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        params_to_fit = [
            {
                'name': name,
                'initial': params['initial'],
                'lower': params['lower'],
                'upper': params['upper']
            }
            for name, params in model_params.items()
        ]

        # 设置仿真参数
        if mode == "idvg":
            fixed_voltage = sim_params['vds']
            sweep_params = (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step'])
        else:  # idvd
            fixed_voltage = sim_params['vgs']
            sweep_params = (sim_params['vds_start'], sim_params['vds_stop'], sim_params['vds_step'])

        if verbose:
            print(f"Starting {mode} fitting with {model_type} model...")
            print(f"Parameters to fit: {[p['name'] for p in params_to_fit]}")

        # 执行拟合
        final_params, voltage_fit, id_fit = fitting_core.perform_fitting(
            fitting_core.get_model_template(model_type, mode),
            data, fixed_voltage, sweep_params, params_to_fit, all_initial_params, mode
        )

        if verbose:
            print("Fitting completed successfully!")
            print("Final parameters:")
            for name, value in final_params.items():
                if name in [p['name'] for p in params_to_fit]:
                    initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                    print(f"  {name}: {value:.6f} (initial: {initial:.6f})")

        # 计算拟合质量指标
        if mode == "idvg":
            voltage_real = data['Vgs'].values
        else:
            voltage_real = data['Vds'].values
        id_real = data['Id'].values

        id_fit_interp = data_utils.interpolate_data(voltage_real, voltage_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)

        if verbose:
            print(f"Fitting quality: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")

        return final_params, (voltage_fit, id_fit), data, (rmse, r_squared)

    except (DataError, SimulationError, FittingError) as e:
        print(f"Error in {mode} fitting: {e}")
        return None, None, None, None
    except Exception as e:
        print(f"Unexpected error in {mode} fitting: {e}")
        return None, None, None, None

def main():
    """主函数"""
    parser = setup_argument_parser()
    args = parser.parse_args()

    print("=== MOSFET Parameter Fitting Tool ===")
    print(f"Mode: {args.mode}")
    print(f"Model: {args.model}")

    try:
        if args.mode in ['idvg', 'idvd']:
            # 单一类型拟合
            data_file = args.idvg_data if args.mode == 'idvg' else args.idvd_data
            result = perform_single_fitting(args.mode, args.model, data_file, args.verbose)

            if result[0] is not None:
                final_params, fit_result, data, quality = result

                # 导出结果
                data_utils.export_results(final_params, args.model, args.mode, args.output)
                print(f"Results exported to {args.output}")

                # 生成图表
                if args.plot:
                    voltage_fit, id_fit = fit_result
                    voltage_col = 'Vgs' if args.mode == 'idvg' else 'Vds'

                    plt.figure(figsize=(10, 6))
                    plt.plot(data[voltage_col], data['Id'], 'bo', markersize=4, label='Measurement Data')
                    plt.plot(voltage_fit, id_fit, 'r-', linewidth=2, label=f'Fitted {args.model} Model')
                    plt.xlabel(f'{voltage_col} (V)')
                    plt.ylabel('Id (A)')
                    plt.title(f'{args.mode.upper()} Fitting Results')
                    plt.grid(True)
                    plt.legend()

                    plot_file = f"{args.mode}_fitting_result.png"
                    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
                    print(f"Plot saved as {plot_file}")

                    rmse, r_squared = quality
                    print(f"Final quality metrics: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
            else:
                print("Fitting failed!")
                sys.exit(1)

        elif args.mode == 'joint':
            # 联合拟合
            print("Joint fitting mode not yet implemented in CLI")
            print("Please use the GUI application for joint fitting")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nFitting interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

    print("=== Fitting completed successfully ===")

if __name__ == "__main__":
    main()
