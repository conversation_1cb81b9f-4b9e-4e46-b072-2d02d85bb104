#!/usr/bin/env python3
"""
MOSFET拟合工具演示脚本
展示所有主要功能的使用方法
"""

import os
import sys
import time
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 导入项目模块
import fitting_core
import config
import data_utils
from exceptions import FittingError

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_subsection(title):
    """打印子章节标题"""
    print(f"\n{'-'*40}")
    print(f" {title}")
    print(f"{'-'*40}")

def demo_data_generation():
    """演示数据生成功能"""
    print_section("数据生成演示")
    
    print("1. 生成IdVg目标数据...")
    os.system("uv run python 1_generate_real_data.py")
    
    print("\n2. 生成IdVd目标数据...")
    os.system("uv run python 2_generate_idvd_data.py")
    
    print("\n3. 检查生成的数据文件...")
    files = ['real_data.csv', 'real_data_idvd.csv', 'real_data_idvd_multi.csv']
    for file in files:
        if os.path.exists(file):
            data = pd.read_csv(file)
            print(f"   {file}: {len(data)} 数据点")
        else:
            print(f"   {file}: 文件不存在")

def demo_single_fitting():
    """演示单一类型拟合"""
    print_section("单一类型拟合演示")
    
    # IdVg拟合演示
    print_subsection("IdVg拟合")
    try:
        # 加载数据
        idvg_data = data_utils.load_measurement_data('real_data.csv', 'idvg')
        print(f"加载IdVg数据: {len(idvg_data)} 个数据点")
        
        # 设置参数
        model_type = "LEVEL=1"
        model_params = config.get_model_params(model_type)
        sim_params = config.get_simulation_params("idvg")
        
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        params_to_fit = [
            {
                'name': name,
                'initial': params['initial'],
                'lower': params['lower'],
                'upper': params['upper']
            }
            for name, params in model_params.items()
        ]
        
        print(f"拟合参数: {[p['name'] for p in params_to_fit]}")
        
        # 执行拟合
        start_time = time.time()
        template = fitting_core.get_model_template(model_type, "idvg")
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, idvg_data, sim_params['vds'], 
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        end_time = time.time()
        
        print(f"拟合完成，耗时: {end_time - start_time:.2f} 秒")
        print("最终参数:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"  {name}: {value:.6f} (初始值: {initial:.6f})")
        
        # 计算拟合质量
        id_real = idvg_data['Id'].values
        vgs_real = idvg_data['Vgs'].values
        id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
    except Exception as e:
        print(f"IdVg拟合失败: {e}")
    
    # IdVd拟合演示
    print_subsection("IdVd拟合")
    try:
        # 加载数据
        idvd_data = data_utils.load_measurement_data('real_data_idvd.csv', 'idvd')
        print(f"加载IdVd数据: {len(idvd_data)} 个数据点")
        
        # 设置参数
        sim_params = config.get_simulation_params("idvd")
        
        # 执行拟合
        start_time = time.time()
        template = fitting_core.get_model_template(model_type, "idvd")
        final_params, vds_fit, id_fit = fitting_core.perform_fitting(
            template, idvd_data, sim_params['vgs'],
            (sim_params['vds_start'], sim_params['vds_stop'], sim_params['vds_step']),
            params_to_fit, all_initial_params, "idvd"
        )
        end_time = time.time()
        
        print(f"拟合完成，耗时: {end_time - start_time:.2f} 秒")
        print("最终参数:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"  {name}: {value:.6f} (初始值: {initial:.6f})")
        
        # 计算拟合质量
        id_real = idvd_data['Id'].values
        vds_real = idvd_data['Vds'].values
        id_fit_interp = data_utils.interpolate_data(vds_real, vds_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
    except Exception as e:
        print(f"IdVd拟合失败: {e}")

def demo_joint_fitting():
    """演示联合拟合"""
    print_section("联合拟合演示")
    
    try:
        # 加载数据
        idvg_data = data_utils.load_measurement_data('real_data.csv', 'idvg')
        idvd_data = data_utils.load_measurement_data('real_data_idvd.csv', 'idvd')
        
        print(f"IdVg数据: {len(idvg_data)} 个数据点")
        print(f"IdVd数据: {len(idvd_data)} 个数据点")
        
        # 设置参数
        model_type = "LEVEL=1"
        model_params = config.get_model_params(model_type)
        idvg_sim_params = config.get_simulation_params("idvg")
        idvd_sim_params = config.get_simulation_params("idvd")
        
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        params_to_fit = [
            {
                'name': name,
                'initial': params['initial'],
                'lower': params['lower'],
                'upper': params['upper']
            }
            for name, params in model_params.items()
        ]
        
        print(f"联合拟合参数: {[p['name'] for p in params_to_fit]}")
        
        # 执行联合拟合
        start_time = time.time()
        final_params, idvg_result, idvd_result = fitting_core.perform_joint_fitting(
            model_type, idvg_data, idvd_data,
            idvg_sim_params['vds'], 
            (idvg_sim_params['vgs_start'], idvg_sim_params['vgs_stop'], idvg_sim_params['vgs_step']),
            idvd_sim_params['vgs'],
            (idvd_sim_params['vds_start'], idvd_sim_params['vds_stop'], idvd_sim_params['vds_step']),
            params_to_fit, all_initial_params
        )
        end_time = time.time()
        
        print(f"联合拟合完成，耗时: {end_time - start_time:.2f} 秒")
        print("最终参数:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"  {name}: {value:.6f} (初始值: {initial:.6f})")
        
        # 计算拟合质量
        vgs_fit, id_idvg_fit = idvg_result
        vds_fit, id_idvd_fit = idvd_result
        
        # IdVg质量
        id_idvg_real = idvg_data['Id'].values
        vgs_real = idvg_data['Vgs'].values
        id_idvg_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_idvg_fit)
        rmse_idvg = data_utils.calculate_rmse(id_idvg_real, id_idvg_fit_interp)
        
        # IdVd质量
        id_idvd_real = idvd_data['Id'].values
        vds_real = idvd_data['Vds'].values
        id_idvd_fit_interp = data_utils.interpolate_data(vds_real, vds_fit, id_idvd_fit)
        rmse_idvd = data_utils.calculate_rmse(id_idvd_real, id_idvd_fit_interp)
        
        print(f"拟合质量:")
        print(f"  IdVg RMSE: {rmse_idvg:.6f} A")
        print(f"  IdVd RMSE: {rmse_idvd:.6f} A")
        
    except Exception as e:
        print(f"联合拟合失败: {e}")

def demo_visualization():
    """演示可视化功能"""
    print_section("可视化演示")
    
    try:
        # 创建综合对比图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # IdVg原始数据
        idvg_data = pd.read_csv('real_data.csv')
        ax1.plot(idvg_data['Vgs'], idvg_data['Id'], 'bo', markersize=4, label='Target Data')
        ax1.set_xlabel('Vgs (V)')
        ax1.set_ylabel('Id (A)')
        ax1.set_title('IdVg Target Data')
        ax1.grid(True)
        ax1.legend()
        
        # IdVd原始数据
        idvd_data = pd.read_csv('real_data_idvd.csv')
        ax2.plot(idvd_data['Vds'], idvd_data['Id'], 'ro', markersize=4, label='Target Data')
        ax2.set_xlabel('Vds (V)')
        ax2.set_ylabel('Id (A)')
        ax2.set_title('IdVd Target Data')
        ax2.grid(True)
        ax2.legend()
        
        # 多Vgs的IdVd数据
        multi_idvd_data = pd.read_csv('real_data_idvd_multi.csv')
        vgs_values = multi_idvd_data['Vgs'].unique()
        colors = ['b', 'g', 'r', 'c']
        for i, vgs in enumerate(vgs_values):
            subset = multi_idvd_data[multi_idvd_data['Vgs'] == vgs]
            ax3.plot(subset['Vds'], subset['Id'], 'o-', color=colors[i % len(colors)], 
                    markersize=3, label=f'Vgs = {vgs}V')
        ax3.set_xlabel('Vds (V)')
        ax3.set_ylabel('Id (A)')
        ax3.set_title('Multi-Vgs IdVd Characteristics')
        ax3.grid(True)
        ax3.legend()
        
        # 参数对比
        models = ['Initial', 'IdVg Fit', 'IdVd Fit', 'Joint Fit']
        vto_values = [2.5, 2.79, 1.50, 1.00]  # 示例值
        kp_values = [0.1, 0.137, 0.180, 0.086]  # 示例值
        
        x = np.arange(len(models))
        width = 0.35
        
        ax4.bar(x - width/2, vto_values, width, label='VTO', alpha=0.8)
        ax4.bar(x + width/2, [v*10 for v in kp_values], width, label='KP×10', alpha=0.8)
        ax4.set_xlabel('Model')
        ax4.set_ylabel('Parameter Value')
        ax4.set_title('Parameter Comparison')
        ax4.set_xticks(x)
        ax4.set_xticklabels(models, rotation=45)
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('demo_visualization.png', dpi=150, bbox_inches='tight')
        print("可视化图表已保存为 'demo_visualization.png'")
        
    except Exception as e:
        print(f"可视化失败: {e}")

def demo_cli_tools():
    """演示命令行工具"""
    print_section("命令行工具演示")
    
    print("1. 显示帮助信息:")
    os.system("uv run python main.py --help")
    
    print("\n2. 执行IdVg拟合:")
    os.system("uv run python main.py --mode idvg --verbose")
    
    print("\n3. 执行IdVd拟合:")
    os.system("uv run python main.py --mode idvd --verbose")

def demo_testing():
    """演示测试功能"""
    print_section("测试功能演示")
    
    print("运行完整测试套件:")
    os.system("uv run python test_suite.py")

def main():
    """主演示函数"""
    print("🚀 MOSFET参数拟合工具 - 完整功能演示")
    print("=" * 60)
    
    # 检查依赖
    try:
        import fitting_core
        import config
        import data_utils
        print("✅ 所有模块导入成功")
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return
    
    # 演示各个功能
    demo_data_generation()
    demo_single_fitting()
    demo_joint_fitting()
    demo_visualization()
    demo_cli_tools()
    demo_testing()
    
    print_section("演示完成")
    print("🎉 所有功能演示完成！")
    print("\n📁 生成的文件:")
    files = [
        'real_data.csv', 'real_data_idvd.csv', 'real_data_idvd_multi.csv',
        'fitted_model.lib', 'demo_visualization.png',
        'idvg_fitting_result.png', 'idvd_fitting_result.png'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (未生成)")
    
    print("\n🔧 使用方法:")
    print("  GUI界面: uv run python gui_app.py")
    print("  命令行: uv run python main.py --help")
    print("  测试: uv run python test_suite.py")

if __name__ == "__main__":
    main()
