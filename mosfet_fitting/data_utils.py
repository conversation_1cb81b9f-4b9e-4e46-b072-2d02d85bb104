"""
数据处理工具模块
"""

import pandas as pd
import numpy as np
from pathlib import Path
from exceptions import DataError

def load_measurement_data(file_path, data_type="idvg"):
    """
    加载测量数据
    
    Args:
        file_path: 数据文件路径
        data_type: 数据类型 "idvg" 或 "idvd"
    
    Returns:
        pd.DataFrame: 加载的数据
    
    Raises:
        DataError: 数据加载或验证失败
    """
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            raise DataError(f"Data file not found: {file_path}")
        
        data = pd.read_csv(file_path)
        
        # 验证数据格式
        if data_type == "idvg":
            required_cols = ['Vgs', 'Id']
        elif data_type == "idvd":
            required_cols = ['Vds', 'Id']
        else:
            raise DataError(f"Unsupported data type: {data_type}")
        
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise DataError(f"Missing required columns: {missing_cols}")
        
        # 检查数据质量
        if len(data) == 0:
            raise DataError("Data file is empty")
        
        # 检查是否有NaN值
        if data[required_cols].isnull().any().any():
            raise DataError("Data contains NaN values")
        
        # 检查电流值是否合理
        id_col = 'Id'
        if (data[id_col] < 0).all():
            # 如果所有电流值都是负数，可能是方向问题
            data[id_col] = np.abs(data[id_col])
            print(f"Warning: All current values were negative, converted to positive")
        
        return data
        
    except pd.errors.EmptyDataError:
        raise DataError(f"Data file is empty or corrupted: {file_path}")
    except pd.errors.ParserError as e:
        raise DataError(f"Failed to parse data file: {e}")
    except Exception as e:
        raise DataError(f"Unexpected error loading data: {e}")

def validate_data_consistency(data, data_type):
    """
    验证数据一致性
    
    Args:
        data: DataFrame数据
        data_type: 数据类型
    
    Raises:
        DataError: 数据不一致
    """
    if data_type == "idvg":
        voltage_col = 'Vgs'
    elif data_type == "idvd":
        voltage_col = 'Vds'
    else:
        raise DataError(f"Unsupported data type: {data_type}")
    
    # 检查电压是否单调递增
    voltages = data[voltage_col].values
    if not np.all(np.diff(voltages) >= 0):
        raise DataError(f"{voltage_col} values are not monotonically increasing")
    
    # 检查是否有重复的电压值
    if len(np.unique(voltages)) != len(voltages):
        raise DataError(f"Duplicate {voltage_col} values found")
    
    # 检查电流值范围
    currents = data['Id'].values
    if np.any(currents < 0):
        print(f"Warning: Negative current values found in {data_type} data")
    
    if np.any(np.abs(currents) > 1000):  # 1000A seems unreasonable for most devices
        print(f"Warning: Very large current values found in {data_type} data")

def interpolate_data(x_target, x_source, y_source):
    """
    数据插值
    
    Args:
        x_target: 目标x值数组
        x_source: 源x值数组
        y_source: 源y值数组
    
    Returns:
        np.array: 插值后的y值
    """
    try:
        return np.interp(x_target, x_source, y_source)
    except Exception as e:
        raise DataError(f"Interpolation failed: {e}")

def calculate_rmse(y_true, y_pred):
    """
    计算均方根误差
    
    Args:
        y_true: 真实值
        y_pred: 预测值
    
    Returns:
        float: RMSE值
    """
    try:
        return np.sqrt(np.mean((y_true - y_pred) ** 2))
    except Exception as e:
        raise DataError(f"RMSE calculation failed: {e}")

def calculate_r_squared(y_true, y_pred):
    """
    计算R²值
    
    Args:
        y_true: 真实值
        y_pred: 预测值
    
    Returns:
        float: R²值
    """
    try:
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        return 1 - (ss_res / ss_tot)
    except Exception as e:
        raise DataError(f"R² calculation failed: {e}")

def export_results(fitted_params, model_type, fitting_type, output_path):
    """
    导出拟合结果 - 按照Power NMOS模板格式

    Args:
        fitted_params: 拟合参数字典
        model_type: 模型类型
        fitting_type: 拟合类型
        output_path: 输出文件路径
    """
    try:
        output_path = Path(output_path)

        # 生成Power NMOS格式的SPICE模型
        subckt_content = generate_power_nmos_model(fitted_params, model_type, fitting_type)

        with open(output_path, 'w') as f:
            f.write(subckt_content)

    except Exception as e:
        raise DataError(f"Failed to export results: {e}")

def generate_power_nmos_model(fitted_params, model_type, fitting_type):
    """
    生成Power NMOS格式的SPICE模型
    """
    from datetime import datetime

    # 获取当前日期
    current_date = datetime.now().strftime("%Y-%m-%d")

    # 根据拟合参数生成模型名称
    model_name = f"FITTED_{model_type}_{fitting_type.upper()}"

    # 提取主要参数
    if model_type == "LEVEL=1":
        vto = fitted_params.get('vto', 2.5)
        kp = fitted_params.get('kp', 0.1)
        # 转换KP单位：从A/V²转换为μA/V²
        kp_micro = kp * 1e6
        level = 3  # 使用LEVEL=3以获得更好的功率器件特性
    else:  # BSIM3
        vto = fitted_params.get('vth0', 2.8)
        u0 = fitted_params.get('u0', 0.06)
        vsat = fitted_params.get('vsat', 1e5)
        # 对于BSIM3，我们需要转换为LEVEL=3的等效参数
        kp_micro = u0 * 600  # 近似转换
        level = 3

    # 生成完整的Power NMOS模型
    model_content = f"""simulator lang=pspice


* Company: Fitted Model Generator
* Project: MOSFET Parameter Fitting
* Component: Fitted {model_type} Power NMOS SPICE Model
* Version: 1.0
* Date: {current_date}
* Description: This model provides a SPICE representation for a fitted NMOS transistor,
*              including parasitic elements and behavioral capacitance models.
*              Fitted using {fitting_type.upper()} characteristics.
*



.SUBCKT {model_name} D G S

* Default capacitance parameters (can be refined with C-V measurements)
.PARAM param_Cgs_0=7.714217890444044e-10
.PARAM param_Cgs_1=4.0280703320039185e-11
.PARAM param_Cgs_2=-1.9046913399780577e-11
.PARAM param_Cgs_3=7.613449317327738e-12
.PARAM param_Cgs_4=-2.158589829283146e-12
.PARAM param_Cgs_5=4.378948437899137e-13
.PARAM param_Cgs_6=-6.563253573101506e-14
.PARAM param_Cgs_7=7.426725097371452e-15
.PARAM param_Cgs_8=-6.360322778350816e-16
.PARAM param_Cgs_9=4.050401173895852e-17
.PARAM param_Cgs_10=-1.835878219157559e-18
.PARAM param_Cgs_11=5.320531326863741e-20
.PARAM param_Cgs_12=-6.173195592391248e-22
.PARAM param_Cgs_13=-1.804176360352064e-23
.PARAM param_Cgs_14=8.341693011018898e-25
.PARAM param_Cgs_15=-5.6068387326219925e-27
.PARAM param_Cgs_16=-4.642548510593262e-28
.PARAM param_Cgs_17=1.671500592963986e-29
.PARAM param_Cgs_18=-2.6865574380795886e-31
.PARAM param_Cgs_19=2.2301000238860847e-33
.PARAM param_Cgs_20=-7.772926433152147e-36
.FUNC Cgs(Vds) {{param_Cgs_0 + param_Cgs_1 * Vds**1 + param_Cgs_2 * Vds**2 + param_Cgs_3 * Vds**3 + param_Cgs_4 * Vds**4 + param_Cgs_5 * Vds**5 + param_Cgs_6 * Vds**6 + param_Cgs_7 * Vds**7 + param_Cgs_8 * Vds**8 + param_Cgs_9 * Vds**9 + param_Cgs_10 * Vds**10 + param_Cgs_11 * Vds**11 + param_Cgs_12 * Vds**12 + param_Cgs_13 * Vds**13 + param_Cgs_14 * Vds**14 + param_Cgs_15 * Vds**15 + param_Cgs_16 * Vds**16 + param_Cgs_17 * Vds**17 + param_Cgs_18 * Vds**18 + param_Cgs_19 * Vds**19 + param_Cgs_20 * Vds**20}}

.PARAM param_Cds_0=1.2852122069908754e-09
.PARAM param_Cds_1=-2.9188493920461405e-10
.PARAM param_Cds_2=2.1635950646854757e-10
.PARAM param_Cds_3=-1.2861570599511894e-10
.PARAM param_Cds_4=5.0506555019473515e-11
.PARAM param_Cds_5=-1.3334411909838581e-11
.PARAM param_Cds_6=2.4353637381836397e-12
.PARAM param_Cds_7=-3.142648251583372e-13
.PARAM param_Cds_8=2.894351829090603e-14
.PARAM param_Cds_9=-1.8932251460039867e-15
.PARAM param_Cds_10=8.524265825857386e-17
.PARAM param_Cds_11=-2.3863343414779638e-18
.PARAM param_Cds_12=2.4659482526305e-20
.PARAM param_Cds_13=8.64732353177672e-22
.PARAM param_Cds_14=-3.567779864022168e-23
.PARAM param_Cds_15=1.842376308523824e-25
.PARAM param_Cds_16=2.037463203169837e-26
.PARAM param_Cds_17=-6.868606283684063e-28
.PARAM param_Cds_18=1.0635980738309712e-29
.PARAM param_Cds_19=-8.575666505271257e-32
.PARAM param_Cds_20=2.916272242132033e-34
.FUNC Cds(Vds) {{param_Cds_0 + param_Cds_1 * Vds**1 + param_Cds_2 * Vds**2 + param_Cds_3 * Vds**3 + param_Cds_4 * Vds**4 + param_Cds_5 * Vds**5 + param_Cds_6 * Vds**6 + param_Cds_7 * Vds**7 + param_Cds_8 * Vds**8 + param_Cds_9 * Vds**9 + param_Cds_10 * Vds**10 + param_Cds_11 * Vds**11 + param_Cds_12 * Vds**12 + param_Cds_13 * Vds**13 + param_Cds_14 * Vds**14 + param_Cds_15 * Vds**15 + param_Cds_16 * Vds**16 + param_Cds_17 * Vds**17 + param_Cds_18 * Vds**18 + param_Cds_19 * Vds**19 + param_Cds_20 * Vds**20}}

.PARAM param_Cgd_0=3.8644326304379207e-10
.PARAM param_Cgd_1=-1.3443113511452803e-10
.PARAM param_Cgd_2=4.6879330716156526e-11
.PARAM param_Cgd_3=-1.1649017101116298e-11
.PARAM param_Cgd_4=1.4636741111032848e-12
.PARAM param_Cgd_5=9.292876620607331e-14
.PARAM param_Cgd_6=-7.358547702953661e-14
.PARAM param_Cgd_7=1.4317395299962158e-14
.PARAM param_Cgd_8=-1.6044091763913606e-15
.PARAM param_Cgd_9=1.1631793481562996e-16
.PARAM param_Cgd_10=-5.5175798801730534e-18
.PARAM param_Cgd_11=1.576888613721507e-19
.PARAM param_Cgd_12=-1.612039260853736e-21
.PARAM param_Cgd_13=-5.807093596670455e-23
.PARAM param_Cgd_14=2.3223650394159455e-24
.PARAM param_Cgd_15=-1.039474536102361e-26
.PARAM param_Cgd_16=-1.3348792921086872e-27
.PARAM param_Cgd_17=4.3391281661301184e-29
.PARAM param_Cgd_18=-6.544256507836825e-31
.PARAM param_Cgd_19=5.1518856681514076e-33
.PARAM param_Cgd_20=-1.7129559129517822e-35
.FUNC Cgd(Vds) {{param_Cgd_0 + param_Cgd_1 * Vds**1 + param_Cgd_2 * Vds**2 + param_Cgd_3 * Vds**3 + param_Cgd_4 * Vds**4 + param_Cgd_5 * Vds**5 + param_Cgd_6 * Vds**6 + param_Cgd_7 * Vds**7 + param_Cgd_8 * Vds**8 + param_Cgd_9 * Vds**9 + param_Cgd_10 * Vds**10 + param_Cgd_11 * Vds**11 + param_Cgd_12 * Vds**12 + param_Cgd_13 * Vds**13 + param_Cgd_14 * Vds**14 + param_Cgd_15 * Vds**15 + param_Cgd_16 * Vds**16 + param_Cgd_17 * Vds**17 + param_Cgd_18 * Vds**18 + param_Cgd_19 * Vds**19 + param_Cgd_20 * Vds**20}}


* Parasitic inductances and resistances (scaled for fitted device)
LD D D_int_L 2n
RD_L_PAR D D_int_L 0.05m
RLD1 D_int_L D_int_MOS 6e-06
RD D_int_MOS D_int_MOS_internal 0.001604688

LG G G_int_L 7.81292696075128e-10
RLG G G_int_L 1.96360271543439
RG G_int_L G_int_MOS 2.5

LS S S_int_L 4n
RS_L_PAR S S_int_L 0.05m
RLS1 S_int_L S_int_MOS 0.00055
RS S_int_MOS S_int_MOS_internal 0.001604688

* Main MOSFET device with fitted parameters
M1 D_int_MOS_internal G_int_MOS S_int_MOS_internal S_int_MOS_internal FITTED_MODEL

* Parasitic diodes
DBGS S_int_MOS_internal G_int_MOS DBGS
DBD S_int_MOS_internal D_int_MOS_internal DBD

* Voltage-dependent capacitances
C_gs S_int_MOS_internal G_int_MOS capacitor C = Cgs(V(D_int_MOS_internal, S_int_MOS_internal))
C_ds D_int_MOS_internal S_int_MOS_internal capacitor C = Cds(V(D_int_MOS_internal, S_int_MOS_internal))
C_gd D_int_MOS_internal G_int_MOS capacitor C = Cgd(V(D_int_MOS_internal, S_int_MOS_internal))

.ENDS {model_name}


* Fitted MOSFET model with extracted parameters
.MODEL FITTED_MODEL NMOS(Vto={vto:.6f} Kp={kp_micro:.6f} Nfs=440000000000 Eta=1000
+ Level={level} L=1e-4 W=1e-4 Gamma=0 Phi=0.6 Is=1e-24
+ Js=0 Pb=0.8 Cj=0 Cjsw=0 Cgso=0 Cgdo=0 Cgbo=0
+ Tox=50e-09 Xj=0
+ U0=600 Vmax=10000 )

* Body diode model (default parameters, can be refined)
.MODEL DBD D(Bv=46.4658 Ibv=2.515978465E-004 Rs=1E-6 Is=2.74564811268e-12
+ N=1 M=0.55 VJ=0.7 Fc=0.5 Cjo=8.7201532e-10 Tt=1.05648165e-08 )

* Gate-source diode model (default parameters)
.MODEL DBGS D(Bv=37.8654 Ibv=0.86572u)

"""

    return model_content
