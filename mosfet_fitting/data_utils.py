"""
数据处理工具模块
"""

import pandas as pd
import numpy as np
from pathlib import Path
from exceptions import DataError

def load_measurement_data(file_path, data_type="idvg"):
    """
    加载测量数据
    
    Args:
        file_path: 数据文件路径
        data_type: 数据类型 "idvg" 或 "idvd"
    
    Returns:
        pd.DataFrame: 加载的数据
    
    Raises:
        DataError: 数据加载或验证失败
    """
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            raise DataError(f"Data file not found: {file_path}")
        
        data = pd.read_csv(file_path)
        
        # 验证数据格式
        if data_type == "idvg":
            required_cols = ['Vgs', 'Id']
        elif data_type == "idvd":
            required_cols = ['Vds', 'Id']
        else:
            raise DataError(f"Unsupported data type: {data_type}")
        
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            raise DataError(f"Missing required columns: {missing_cols}")
        
        # 检查数据质量
        if len(data) == 0:
            raise DataError("Data file is empty")
        
        # 检查是否有NaN值
        if data[required_cols].isnull().any().any():
            raise DataError("Data contains NaN values")
        
        # 检查电流值是否合理
        id_col = 'Id'
        if (data[id_col] < 0).all():
            # 如果所有电流值都是负数，可能是方向问题
            data[id_col] = np.abs(data[id_col])
            print(f"Warning: All current values were negative, converted to positive")
        
        return data
        
    except pd.errors.EmptyDataError:
        raise DataError(f"Data file is empty or corrupted: {file_path}")
    except pd.errors.ParserError as e:
        raise DataError(f"Failed to parse data file: {e}")
    except Exception as e:
        raise DataError(f"Unexpected error loading data: {e}")

def validate_data_consistency(data, data_type):
    """
    验证数据一致性
    
    Args:
        data: DataFrame数据
        data_type: 数据类型
    
    Raises:
        DataError: 数据不一致
    """
    if data_type == "idvg":
        voltage_col = 'Vgs'
    elif data_type == "idvd":
        voltage_col = 'Vds'
    else:
        raise DataError(f"Unsupported data type: {data_type}")
    
    # 检查电压是否单调递增
    voltages = data[voltage_col].values
    if not np.all(np.diff(voltages) >= 0):
        raise DataError(f"{voltage_col} values are not monotonically increasing")
    
    # 检查是否有重复的电压值
    if len(np.unique(voltages)) != len(voltages):
        raise DataError(f"Duplicate {voltage_col} values found")
    
    # 检查电流值范围
    currents = data['Id'].values
    if np.any(currents < 0):
        print(f"Warning: Negative current values found in {data_type} data")
    
    if np.any(np.abs(currents) > 1000):  # 1000A seems unreasonable for most devices
        print(f"Warning: Very large current values found in {data_type} data")

def interpolate_data(x_target, x_source, y_source):
    """
    数据插值
    
    Args:
        x_target: 目标x值数组
        x_source: 源x值数组
        y_source: 源y值数组
    
    Returns:
        np.array: 插值后的y值
    """
    try:
        return np.interp(x_target, x_source, y_source)
    except Exception as e:
        raise DataError(f"Interpolation failed: {e}")

def calculate_rmse(y_true, y_pred):
    """
    计算均方根误差
    
    Args:
        y_true: 真实值
        y_pred: 预测值
    
    Returns:
        float: RMSE值
    """
    try:
        return np.sqrt(np.mean((y_true - y_pred) ** 2))
    except Exception as e:
        raise DataError(f"RMSE calculation failed: {e}")

def calculate_r_squared(y_true, y_pred):
    """
    计算R²值
    
    Args:
        y_true: 真实值
        y_pred: 预测值
    
    Returns:
        float: R²值
    """
    try:
        ss_res = np.sum((y_true - y_pred) ** 2)
        ss_tot = np.sum((y_true - np.mean(y_true)) ** 2)
        return 1 - (ss_res / ss_tot)
    except Exception as e:
        raise DataError(f"R² calculation failed: {e}")

def export_results(fitted_params, model_type, fitting_type, output_path):
    """
    导出拟合结果
    
    Args:
        fitted_params: 拟合参数字典
        model_type: 模型类型
        fitting_type: 拟合类型
        output_path: 输出文件路径
    """
    try:
        output_path = Path(output_path)
        
        # 生成SPICE子电路
        if model_type == "BSIM3":
            terminals = "D G S B"
            level = 8
        else:  # LEVEL=1
            terminals = "D G S"
            level = 1
        
        params_str = "\n".join([f"+ {name:<10} = {val:.6g}" for name, val in fitted_params.items()])
        
        subckt_content = f""".SUBCKT {model_type}_Fitted_{fitting_type} {terminals}
    .MODEL MyMOS NMOS (
    + LEVEL = {level}
{params_str}
    )
    M1 {terminals} MyMOS L=1u W=10u
.ENDS {model_type}_Fitted_{fitting_type}
"""
        
        with open(output_path, 'w') as f:
            f.write(subckt_content)
            
    except Exception as e:
        raise DataError(f"Failed to export results: {e}")
