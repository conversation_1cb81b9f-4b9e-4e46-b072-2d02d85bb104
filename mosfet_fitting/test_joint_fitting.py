#!/usr/bin/env python3
"""
测试联合拟合功能的脚本
"""

import pandas as pd
import matplotlib.pyplot as plt
import fitting_core

def test_joint_fitting():
    """测试联合拟合功能"""
    print("=== Testing Joint Fitting (IdVg + IdVd) ===")
    
    # 1. 加载数据
    print("1. Loading data...")
    try:
        idvg_data = pd.read_csv('real_data.csv')
        idvd_data = pd.read_csv('real_data_idvd.csv')
        print(f"   IdVg data: {len(idvg_data)} points")
        print(f"   IdVd data: {len(idvd_data)} points")
    except FileNotFoundError as e:
        print(f"   Error: {e}")
        print("   Please run 1_generate_real_data.py and 2_generate_idvd_data.py first.")
        return
    
    # 2. 设置拟合参数
    print("\n2. Setting up joint fitting parameters...")
    model_type = "LEVEL=1"
    
    # IdVg参数
    idvg_vds = 15.0
    idvg_sweep_params = (0.0, 10.0, 0.2)
    
    # IdVd参数
    idvd_vgs = 5.0
    idvd_sweep_params = (0.0, 20.0, 0.5)
    
    # 模型参数
    all_initial_params = {
        'vto': 2.5,
        'kp': 0.1
    }
    
    # 待拟合参数
    params_to_fit = [
        {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
        {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
    ]
    
    print(f"   Model: {model_type}")
    print(f"   IdVg: Vds={idvg_vds}V, Vgs sweep {idvg_sweep_params}")
    print(f"   IdVd: Vgs={idvd_vgs}V, Vds sweep {idvd_sweep_params}")
    print(f"   Parameters to fit: {[p['name'] for p in params_to_fit]}")
    
    # 3. 测试初始仿真
    print("\n3. Testing initial simulations...")
    try:
        # IdVg初始仿真
        idvg_template = fitting_core.get_model_template(model_type, "idvg")
        vgs_sim, id_idvg_sim = fitting_core.run_spice_simulation(
            idvg_template, all_initial_params, idvg_sweep_params, idvg_vds, "idvg"
        )
        
        # IdVd初始仿真
        idvd_template = fitting_core.get_model_template(model_type, "idvd")
        vds_sim, id_idvd_sim = fitting_core.run_spice_simulation(
            idvd_template, all_initial_params, idvd_sweep_params, idvd_vgs, "idvd"
        )
        
        if len(vgs_sim) > 0 and len(vds_sim) > 0:
            print(f"   IdVg simulation: {len(vgs_sim)} points")
            print(f"   IdVd simulation: {len(vds_sim)} points")
        else:
            print("   Error: Initial simulations failed")
            return
    except Exception as e:
        print(f"   Error in initial simulations: {e}")
        return
    
    # 4. 执行联合拟合
    print("\n4. Starting joint fitting process...")
    try:
        final_params, idvg_fit_result, idvd_fit_result = fitting_core.perform_joint_fitting(
            model_type, idvg_data, idvd_data, 
            idvg_vds, idvg_sweep_params, idvd_vgs, idvd_sweep_params,
            params_to_fit, all_initial_params
        )
        
        vgs_fit, id_idvg_fit = idvg_fit_result
        vds_fit, id_idvd_fit = idvd_fit_result
        
        print("   Joint fitting completed successfully!")
        print("\n   Final parameters:")
        for name, value in final_params.items():
            if name in [p['name'] for p in params_to_fit]:
                initial = next(p['initial'] for p in params_to_fit if p['name'] == name)
                print(f"     {name}: {value:.6f} (initial: {initial:.6f})")
            else:
                print(f"     {name}: {value:.6f} (fixed)")
                
    except Exception as e:
        print(f"   Error during joint fitting: {e}")
        return
    
    # 5. 绘制结果
    print("\n5. Plotting results...")
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # IdVg图
        ax1.plot(idvg_data['Vgs'], idvg_data['Id'], 'bo', markersize=4, label='Target Data')
        ax1.plot(vgs_sim, id_idvg_sim, 'g--', linewidth=2, label='Initial Model')
        ax1.plot(vgs_fit, id_idvg_fit, 'r-', linewidth=2, label='Joint Fitted Model')
        ax1.set_xlabel('Vgs (V)')
        ax1.set_ylabel('Id (A)')
        ax1.set_title(f'IdVg Fitting Results (Vds = {idvg_vds}V)')
        ax1.grid(True)
        ax1.legend()
        
        # IdVd图
        ax2.plot(idvd_data['Vds'], idvd_data['Id'], 'bo', markersize=4, label='Target Data')
        ax2.plot(vds_sim, id_idvd_sim, 'g--', linewidth=2, label='Initial Model')
        ax2.plot(vds_fit, id_idvd_fit, 'r-', linewidth=2, label='Joint Fitted Model')
        ax2.set_xlabel('Vds (V)')
        ax2.set_ylabel('Id (A)')
        ax2.set_title(f'IdVd Fitting Results (Vgs = {idvd_vgs}V)')
        ax2.grid(True)
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig('joint_fitting_test.png', dpi=150, bbox_inches='tight')
        print("   Plot saved as 'joint_fitting_test.png'")
        
        # 计算拟合误差
        # IdVg RMSE
        id_idvg_real = idvg_data['Id'].values
        if len(id_idvg_fit) == len(id_idvg_real):
            rmse_idvg = ((id_idvg_fit - id_idvg_real) ** 2).mean() ** 0.5
            print(f"   IdVg RMSE: {rmse_idvg:.6f} A")
        
        # IdVd RMSE
        id_idvd_real = idvd_data['Id'].values
        if len(id_idvd_fit) == len(id_idvd_real):
            rmse_idvd = ((id_idvd_fit - id_idvd_real) ** 2).mean() ** 0.5
            print(f"   IdVd RMSE: {rmse_idvd:.6f} A")
        
    except Exception as e:
        print(f"   Error in plotting: {e}")
    
    print("\n=== Joint Fitting Test Completed ===")

if __name__ == "__main__":
    test_joint_fitting()
