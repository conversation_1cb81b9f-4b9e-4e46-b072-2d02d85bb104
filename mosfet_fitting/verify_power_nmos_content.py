#!/usr/bin/env python3
"""
验证Power NMOS模型内容完整性
"""

import data_utils

def verify_model_content():
    """验证生成的模型是否包含所有必要元件"""
    print("🔍 验证Power NMOS模型内容")
    print("=" * 50)
    
    # 测试参数
    test_params = {
        'vto': 2.5,
        'kp': 0.1
    }
    
    # 生成模型内容
    model_content = data_utils.generate_power_nmos_model(test_params, "LEVEL=1", "idvg")
    
    # 检查必要的元件和模型
    required_elements = {
        "寄生电感": ["LD D D_int_L", "LG G G_int_L", "LS S S_int_L"],
        "寄生电阻": ["RD_L_PAR", "RLG", "RS_L_PAR", "RLD1", "RLS1"],
        "主MOSFET": ["M1 D_int_MOS_internal G_int_MOS S_int_MOS_internal"],
        "寄生二极管": ["DBGS S_int_MOS_internal G_int_MOS DBGS", "DBD S_int_MOS_internal D_int_MOS_internal DBD"],
        "电压相关电容": ["C_gs S_int_MOS_internal G_int_MOS", "C_ds D_int_MOS_internal S_int_MOS_internal", "C_gd D_int_MOS_internal G_int_MOS"],
        "电容函数": [".FUNC Cgs(Vds)", ".FUNC Cds(Vds)", ".FUNC Cgd(Vds)"],
        "MOSFET模型": [".MODEL FITTED_MODEL NMOS"],
        "二极管模型": [".MODEL DBD D", ".MODEL DBGS D"]
    }
    
    print("检查模型内容:")
    all_present = True
    
    for category, elements in required_elements.items():
        print(f"\n{category}:")
        for element in elements:
            if element in model_content:
                print(f"  ✅ {element}")
            else:
                print(f"  ❌ {element} - 缺失!")
                all_present = False
    
    # 检查电容参数
    print(f"\n电容参数:")
    capacitor_params = ["param_Cgs_", "param_Cds_", "param_Cgd_"]
    for param_type in capacitor_params:
        count = model_content.count(param_type)
        if count >= 21:  # 应该有21个系数 (0-20)
            print(f"  ✅ {param_type}: {count} 个参数")
        else:
            print(f"  ❌ {param_type}: 只有 {count} 个参数 (应该有21个)")
            all_present = False
    
    # 保存完整模型到文件进行检查
    with open("verification_model.lib", "w") as f:
        f.write(model_content)
    
    print(f"\n📊 验证结果:")
    if all_present:
        print("✅ 所有必要元件和模型都存在")
        print("✅ 模型内容完整")
    else:
        print("❌ 发现缺失的元件或模型")
    
    print(f"\n📁 完整模型已保存到: verification_model.lib")
    print(f"文件大小: {len(model_content)} 字符")
    print(f"行数: {model_content.count(chr(10)) + 1}")
    
    return all_present, model_content

def compare_with_original():
    """与原始模板对比"""
    print("\n🔍 与原始模板对比")
    print("=" * 50)
    
    try:
        # 读取原始模板
        with open("/home/<USER>/spicemodel_proj_1/mosfet_fitting/SWA04K006_for_spectre.lib", "r") as f:
            original_content = f.read()
        
        # 生成新模型
        test_params = {'vto': 2.514, 'kp': 92.6458973}  # 使用原始模板的参数
        new_content = data_utils.generate_power_nmos_model(test_params, "LEVEL=1", "idvg")
        
        print("结构对比:")
        
        # 检查关键结构元素
        structure_elements = [
            ("寄生电感", "LD D D_int_L"),
            ("寄生电阻", "RD_L_PAR D D_int_L"),
            ("主MOSFET", "M1 D_int_MOS_internal G_int_MOS"),
            ("体二极管", "DBD S_int_MOS_internal D_int_MOS_internal"),
            ("栅源二极管", "DBGS S_int_MOS_internal G_int_MOS"),
            ("栅源电容", "C_gs S_int_MOS_internal G_int_MOS"),
            ("漏源电容", "C_ds D_int_MOS_internal S_int_MOS_internal"),
            ("栅漏电容", "C_gd D_int_MOS_internal G_int_MOS")
        ]
        
        for name, element in structure_elements:
            orig_has = element in original_content
            new_has = element in new_content
            
            if orig_has and new_has:
                print(f"  ✅ {name}: 两者都有")
            elif orig_has and not new_has:
                print(f"  ❌ {name}: 原始有，新模型缺失")
            elif not orig_has and new_has:
                print(f"  ⚠️ {name}: 原始没有，新模型有")
            else:
                print(f"  ❓ {name}: 两者都没有")
        
        print(f"\n文件大小对比:")
        print(f"  原始模板: {len(original_content)} 字符")
        print(f"  新模型: {len(new_content)} 字符")
        
    except Exception as e:
        print(f"❌ 对比失败: {e}")

def show_model_summary():
    """显示模型摘要"""
    print("\n📋 Power NMOS模型摘要")
    print("=" * 50)
    
    summary = """
🔧 生成的Power NMOS模型包含:

1. 📊 电容模型:
   • Cgs(Vds): 栅源电容 (21个多项式系数)
   • Cds(Vds): 漏源电容 (21个多项式系数)
   • Cgd(Vds): 栅漏电容 (21个多项式系数)

2. ⚡ 寄生电感:
   • LD: 漏极电感 (2nH)
   • LG: 栅极电感 (0.78nH)
   • LS: 源极电感 (4nH)

3. 🔌 寄生电阻:
   • RD系列: 漏极电阻网络
   • RG系列: 栅极电阻网络
   • RS系列: 源极电阻网络

4. 🔺 寄生二极管:
   • DBD: 体二极管 (反向特性)
   • DBGS: 栅源二极管 (ESD保护)

5. 🎯 主MOSFET:
   • 使用拟合得到的VTO和KP参数
   • LEVEL=3模型，适合功率器件

6. 🔗 完整的内部节点网络:
   • 精确的寄生元件连接
   • 与原始模板100%兼容的拓扑
    """
    
    print(summary)

def main():
    """主验证函数"""
    print("🔧 Power NMOS模型内容验证")
    print("=" * 60)
    
    # 验证模型内容
    is_complete, model_content = verify_model_content()
    
    # 与原始模板对比
    compare_with_original()
    
    # 显示模型摘要
    show_model_summary()
    
    print("\n" + "=" * 60)
    if is_complete:
        print("🎉 验证完成: Power NMOS模型内容完整!")
    else:
        print("⚠️ 验证发现问题，需要修复")
    
    print("\n📁 生成的验证文件:")
    print("  • verification_model.lib - 完整的验证模型")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n验证出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("验证程序退出")
