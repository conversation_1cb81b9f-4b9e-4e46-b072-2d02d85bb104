"""
配置文件 - 定义默认参数和设置
"""

# 默认模型参数
DEFAULT_LEVEL1_PARAMS = {
    'vto': {'initial': 2.5, 'lower': 1.0, 'upper': 4.0, 'description': 'Threshold voltage'},
    'kp': {'initial': 0.1, 'lower': 0.01, 'upper': 0.5, 'description': 'Transconductance parameter'}
}

DEFAULT_BSIM3_PARAMS = {
    'vth0': {'initial': 3.0, 'lower': 2.5, 'upper': 3.5, 'description': 'Threshold voltage'},
    'u0': {'initial': 0.055, 'lower': 0.04, 'upper': 0.07, 'description': 'Low-field mobility'},
    'vsat': {'initial': 85000, 'lower': 70000, 'upper': 100000, 'description': 'Saturation velocity'},
    'k1': {'initial': 0.6, 'lower': 0.4, 'upper': 0.8, 'description': 'Body effect coefficient'},
    'k2': {'initial': 0.02, 'lower': -0.05, 'upper': 0.1, 'description': 'Body effect coefficient'},
    'eta0': {'initial': 0.1, 'lower': 0.05, 'upper': 0.2, 'description': 'DIBL coefficient'},
    'rdsw': {'initial': 120, 'lower': 80, 'upper': 200, 'description': 'Source/drain resistance'}
}

# 默认仿真参数
DEFAULT_IDVG_PARAMS = {
    'vds': 15.0,
    'vgs_start': 0.0,
    'vgs_stop': 10.0,
    'vgs_step': 0.2
}

DEFAULT_IDVD_PARAMS = {
    'vgs': 5.0,
    'vds_start': 0.0,
    'vds_stop': 20.0,
    'vds_step': 0.5
}

# 拟合算法参数
FITTING_CONFIG = {
    'method': 'trf',
    'verbose': 0,
    'max_nfev': 1000,
    'ftol': 1e-8,
    'xtol': 1e-8
}

# 文件路径配置
FILE_PATHS = {
    'idvg_target_circuit': 'target_device.cir',
    'idvd_target_circuit': 'target_device_idvd.cir',
    'idvg_data': 'real_data.csv',
    'idvd_data': 'real_data_idvd.csv',
    'multi_idvd_data': 'real_data_idvd_multi.csv'
}

# GUI配置
GUI_CONFIG = {
    'window_size': '1024x768',
    'plot_dpi': 100,
    'marker_size': 4,
    'line_width': 2
}

# 错误处理配置
ERROR_CONFIG = {
    'max_simulation_retries': 3,
    'simulation_timeout': 30,  # seconds
    'large_error_threshold': 1e6
}

def get_model_params(model_type):
    """获取指定模型类型的默认参数"""
    if model_type == "LEVEL=1":
        return DEFAULT_LEVEL1_PARAMS.copy()
    elif model_type == "BSIM3":
        return DEFAULT_BSIM3_PARAMS.copy()
    else:
        raise ValueError(f"Unsupported model type: {model_type}")

def get_simulation_params(sim_type):
    """获取指定仿真类型的默认参数"""
    if sim_type == "idvg":
        return DEFAULT_IDVG_PARAMS.copy()
    elif sim_type == "idvd":
        return DEFAULT_IDVD_PARAMS.copy()
    else:
        raise ValueError(f"Unsupported simulation type: {sim_type}")

def validate_params(params, param_config):
    """验证参数是否在合理范围内"""
    errors = []
    for name, value in params.items():
        if name in param_config:
            config = param_config[name]
            if not (config['lower'] <= value <= config['upper']):
                errors.append(f"Parameter '{name}' value {value} is outside bounds [{config['lower']}, {config['upper']}]")
    return errors
