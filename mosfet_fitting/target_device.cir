* Target Device Definition and Test Bench
.title Target Power MOSFET for Characterization

* This file defines the "physical" device whose parameters we want to discover.
* We are "hiding" the true parameters here.
.SUBCKT TargetMOSFET D G S
    .MODEL MOS_TARGET NMOS (
    + LEVEL = 1
    + VTO = 2.8     ; The "secret" true threshold voltage
    + KP = 0.12     ; The "secret" true transconductance
    + LAMBDA = 0.01 ; The "secret" channel length modulation
    )
    M1 D G S S MOS_TARGET L=1u W=10u
.ENDS TargetMOSFET

* Test Bench to get Id-Vgs curve
X1 D_node G_node 0 TargetMOSFET
Vds D_node 0 DC 15V ; Use a different Vds for realism
Vgs G_node 0 DC 0V

* DC Sweep
.DC Vgs 0 10 0.2

.control
    run
    wrdata temp_data.txt v(g_node) I(Vds)
.endc
.end
