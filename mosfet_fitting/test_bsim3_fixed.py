#!/usr/bin/env python3
"""
测试修复后的BSIM3拟合功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import fitting_core
import config
import data_utils

def test_bsim3_with_matching_data():
    """使用匹配的BSIM3数据测试拟合"""
    print("🧪 测试BSIM3模型与BSIM3数据的拟合")
    print("=" * 50)
    
    # 测试IdVg拟合
    print("1. 测试BSIM3 IdVg拟合...")
    try:
        # 加载BSIM3 IdVg数据
        idvg_data = pd.read_csv('real_data_bsim3_idvg.csv')
        print(f"   加载BSIM3 IdVg数据: {len(idvg_data)} 个数据点")
        
        # 获取BSIM3参数配置
        model_params = config.get_model_params("BSIM3")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置拟合参数（选择关键参数）
        params_to_fit = [
            {'name': 'vth0', 'initial': 2.8, 'lower': 2.0, 'upper': 4.0},
            {'name': 'u0', 'initial': 0.06, 'lower': 0.03, 'upper': 0.08},
            {'name': 'vsat', 'initial': 1e5, 'lower': 5e4, 'upper': 1.5e5}
        ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        print(f"   拟合参数: {[p['name'] for p in params_to_fit]}")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, idvg_data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        print("   ✅ IdVg拟合完成")
        print("   拟合结果:")
        print("   参数名    目标值      初始值      拟合值")
        print("   " + "-" * 45)
        
        target_values = {'vth0': 3.2, 'u0': 0.055, 'vsat': 85000}
        for param in params_to_fit:
            name = param['name']
            target_val = target_values[name]
            init_val = param['initial']
            fit_val = final_params[name]
            print(f"   {name:<8} {target_val:>10.3f} {init_val:>10.3f} {fit_val:>10.3f}")
        
        # 计算拟合质量
        vgs_real = idvg_data['Vgs'].values
        id_real = idvg_data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"\n   拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.semilogy(vgs_real, id_real, 'bo', markersize=4, label='BSIM3 Target Data')
        plt.semilogy(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVg Fitting (Log Scale)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 3, 2)
        plt.plot(vgs_real, id_real, 'bo', markersize=4, label='BSIM3 Target Data')
        plt.plot(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVg Fitting (Linear Scale)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 3, 3)
        residuals = id_fit_interp - id_real
        plt.plot(vgs_real, residuals, 'go', markersize=3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('Vgs (V)')
        plt.ylabel('Residuals (A)')
        plt.title('Fitting Residuals')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('bsim3_fixed_idvg.png', dpi=150, bbox_inches='tight')
        print("   📊 IdVg拟合结果保存为 'bsim3_fixed_idvg.png'")
        
    except Exception as e:
        print(f"   ❌ IdVg拟合失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试IdVd拟合
    print("\n2. 测试BSIM3 IdVd拟合...")
    try:
        # 加载BSIM3 IdVd数据
        idvd_data = pd.read_csv('real_data_bsim3_idvd.csv')
        print(f"   加载BSIM3 IdVd数据: {len(idvd_data)} 个数据点")
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvd")
        template = fitting_core.get_model_template("BSIM3", "idvd")
        
        # 执行拟合
        final_params, vds_fit, id_fit = fitting_core.perform_fitting(
            template, idvd_data, sim_params['vgs'],
            (sim_params['vds_start'], sim_params['vds_stop'], sim_params['vds_step']),
            params_to_fit, all_initial_params, "idvd"
        )
        
        print("   ✅ IdVd拟合完成")
        print("   拟合结果:")
        print("   参数名    目标值      初始值      拟合值")
        print("   " + "-" * 45)
        
        for param in params_to_fit:
            name = param['name']
            target_val = target_values[name]
            init_val = param['initial']
            fit_val = final_params[name]
            print(f"   {name:<8} {target_val:>10.3f} {init_val:>10.3f} {fit_val:>10.3f}")
        
        # 计算拟合质量
        vds_real = idvd_data['Vds'].values
        id_real = idvd_data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vds_real, vds_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"\n   拟合质量: RMSE = {rmse:.6f} A, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.plot(vds_real, id_real, 'bo', markersize=4, label='BSIM3 Target Data')
        plt.plot(vds_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
        plt.xlabel('Vds (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 IdVd Fitting')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        residuals = id_fit_interp - id_real
        plt.plot(vds_real, residuals, 'go', markersize=3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('Vds (V)')
        plt.ylabel('Residuals (A)')
        plt.title('Fitting Residuals')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('bsim3_fixed_idvd.png', dpi=150, bbox_inches='tight')
        print("   📊 IdVd拟合结果保存为 'bsim3_fixed_idvd.png'")
        
    except Exception as e:
        print(f"   ❌ IdVd拟合失败: {e}")
        import traceback
        traceback.print_exc()

def compare_model_data_compatibility():
    """比较不同模型和数据的兼容性"""
    print("\n🔍 模型-数据兼容性分析")
    print("=" * 50)
    
    test_cases = [
        ("LEVEL=1", "real_data.csv", "LEVEL=1模型 + LEVEL=1数据"),
        ("LEVEL=1", "real_data_bsim3_idvg.csv", "LEVEL=1模型 + BSIM3数据"),
        ("BSIM3", "real_data.csv", "BSIM3模型 + LEVEL=1数据"),
        ("BSIM3", "real_data_bsim3_idvg.csv", "BSIM3模型 + BSIM3数据"),
    ]
    
    results = []
    
    for model_type, data_file, description in test_cases:
        print(f"\n测试: {description}")
        
        try:
            # 加载数据
            data = pd.read_csv(data_file)
            
            # 获取参数配置
            model_params = config.get_model_params(model_type)
            all_initial_params = {name: params['initial'] for name, params in model_params.items()}
            
            # 设置拟合参数
            if model_type == "LEVEL=1":
                params_to_fit = [
                    {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
                    {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
                ]
            else:  # BSIM3
                params_to_fit = [
                    {'name': 'vth0', 'initial': 2.8, 'lower': 2.0, 'upper': 4.0},
                    {'name': 'u0', 'initial': 0.06, 'lower': 0.03, 'upper': 0.08}
                ]
            
            # 仿真参数
            sim_params = config.get_simulation_params("idvg")
            template = fitting_core.get_model_template(model_type, "idvg")
            
            # 执行拟合
            final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
                template, data, sim_params['vds'],
                (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
                params_to_fit, all_initial_params, "idvg"
            )
            
            # 计算拟合质量
            vgs_real = data['Vgs'].values
            id_real = data['Id'].values
            id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
            rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
            r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
            
            results.append((description, rmse, r_squared, "✅"))
            print(f"   ✅ 成功: RMSE = {rmse:.6f}, R² = {r_squared:.6f}")
            
        except Exception as e:
            results.append((description, float('inf'), -float('inf'), "❌"))
            print(f"   ❌ 失败: {e}")
    
    # 总结结果
    print("\n📊 兼容性测试总结:")
    print("=" * 70)
    print(f"{'测试案例':<30} {'RMSE':<12} {'R²':<10} {'状态'}")
    print("-" * 70)
    
    for description, rmse, r_squared, status in results:
        rmse_str = f"{rmse:.6f}" if rmse != float('inf') else "Failed"
        r2_str = f"{r_squared:.6f}" if r_squared != -float('inf') else "Failed"
        print(f"{description:<30} {rmse_str:<12} {r2_str:<10} {status}")

def main():
    """主测试函数"""
    print("🔧 BSIM3拟合问题修复测试")
    print("=" * 60)
    
    # 检查必要文件
    import os
    required_files = ['real_data_bsim3_idvg.csv', 'real_data_bsim3_idvd.csv']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少BSIM3数据文件: {missing_files}")
        print("请先运行: uv run python 3_generate_bsim3_data.py")
        return
    
    # 执行测试
    test_bsim3_with_matching_data()
    compare_model_data_compatibility()
    
    print("\n" + "=" * 60)
    print("🎯 修复总结:")
    print("✅ 问题根源: 模型和数据不匹配")
    print("✅ 解决方案: 为BSIM3模型生成对应的目标数据")
    print("✅ 建议: 在GUI中根据选择的模型加载对应的数据文件")
    print("\n📋 使用指南:")
    print("• LEVEL=1模型 → 使用 real_data.csv 或 real_data_idvd.csv")
    print("• BSIM3模型 → 使用 real_data_bsim3_idvg.csv 或 real_data_bsim3_idvd.csv")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("测试程序退出")
