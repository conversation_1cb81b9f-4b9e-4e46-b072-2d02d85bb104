#!/usr/bin/env python3
"""
生成BSIM3模型的目标数据
"""

import subprocess
import numpy as np
import pandas as pd
import os

def generate_bsim3_idvg_data(output_csv='real_data_bsim3_idvg.csv'):
    """生成BSIM3 IdVg目标数据"""
    print("--- 生成BSIM3 IdVg目标数据 ---")
    
    # 创建BSIM3目标器件电路
    bsim3_circuit = """* BSIM3 Target Device for IdVg Characteristics
.title BSIM3 Target MOSFET for IdVg Characterization

.SUBCKT BSIM3TargetMOSFET D G S B
    .MODEL MOS_BSIM3_TARGET NMOS (
    + LEVEL = 8
    * --- Target BSIM3 parameters ---
    + VTH0 = 3.2
    + U0 = 0.055
    + VSAT = 85000
    + K1 = 0.6
    + K2 = 0.02
    + ETA0 = 0.1
    + RDSW = 120
    * --- Other BSIM3 parameters ---
    + LINT = 0.1u WINT = 0.1u
    + TOX = 1.5e-8
    + NCH = 1.7e17
    )
    M1 D G S B MOS_BSIM3_TARGET L=1u W=10u
.ENDS BSIM3TargetMOSFET

* Test Bench for IdVg
X1 D_node G_node 0 0 BSIM3TargetMOSFET
Vds D_node 0 DC 15V
Vgs G_node 0 DC 0V

* DC Sweep - sweep Vgs from 0 to 10V
.DC Vgs 0 10 0.2

.control
    run
    wrdata temp_bsim3_idvg.txt v(g_node) I(Vds)
.endc
.end"""
    
    # 写入电路文件
    with open('temp_bsim3_idvg.cir', 'w') as f:
        f.write(bsim3_circuit)
    
    temp_data_file = 'temp_bsim3_idvg.txt'
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)
    
    # 运行NGSPICE
    try:
        subprocess.run(
            "ngspice -b temp_bsim3_idvg.cir",
            shell=True, check=True, capture_output=True, text=True
        )
    except subprocess.CalledProcessError as e:
        print(f"NGSPICE failed during BSIM3 IdVg data generation:\n{e.stderr}")
        return
    
    # 处理输出数据
    raw_data = np.loadtxt(temp_data_file)
    vgs_real = raw_data[:, 0]
    id_real = np.abs(raw_data[:, 1])  # 取绝对值确保正电流
    
    # 添加少量噪声
    noise_scale = np.abs(id_real) * 0.03 + 1e-12
    id_real_noisy = id_real + np.random.normal(0, noise_scale)
    
    # 确保电流为正
    id_real_noisy = np.abs(id_real_noisy)
    
    # 保存数据
    df = pd.DataFrame({'Vgs': vgs_real, 'Id': id_real_noisy})
    df.to_csv(output_csv, index=False)
    
    print(f"成功生成BSIM3 IdVg数据: {len(df)} 个数据点")
    print(f"Vgs范围: {vgs_real.min():.1f} to {vgs_real.max():.1f} V")
    print(f"Id范围: {id_real_noisy.min():.3e} to {id_real_noisy.max():.3e} A")
    print(f"数据保存到: {output_csv}")
    
    # 清理临时文件
    for temp_file in ['temp_bsim3_idvg.cir', temp_data_file]:
        if os.path.exists(temp_file):
            os.remove(temp_file)

def generate_bsim3_idvd_data(output_csv='real_data_bsim3_idvd.csv'):
    """生成BSIM3 IdVd目标数据"""
    print("\n--- 生成BSIM3 IdVd目标数据 ---")
    
    # 创建BSIM3目标器件电路
    bsim3_circuit = """* BSIM3 Target Device for IdVd Characteristics
.title BSIM3 Target MOSFET for IdVd Characterization

.SUBCKT BSIM3TargetMOSFET D G S B
    .MODEL MOS_BSIM3_TARGET NMOS (
    + LEVEL = 8
    * --- Target BSIM3 parameters ---
    + VTH0 = 3.2
    + U0 = 0.055
    + VSAT = 85000
    + K1 = 0.6
    + K2 = 0.02
    + ETA0 = 0.1
    + RDSW = 120
    * --- Other BSIM3 parameters ---
    + LINT = 0.1u WINT = 0.1u
    + TOX = 1.5e-8
    + NCH = 1.7e17
    )
    M1 D G S B MOS_BSIM3_TARGET L=1u W=10u
.ENDS BSIM3TargetMOSFET

* Test Bench for IdVd
X1 D_node G_node 0 0 BSIM3TargetMOSFET
Vds D_node 0 DC 0V
Vgs G_node 0 DC 6V

* DC Sweep - sweep Vds from 0 to 20V
.DC Vds 0 20 0.5

.control
    run
    wrdata temp_bsim3_idvd.txt v(d_node) I(Vds)
.endc
.end"""
    
    # 写入电路文件
    with open('temp_bsim3_idvd.cir', 'w') as f:
        f.write(bsim3_circuit)
    
    temp_data_file = 'temp_bsim3_idvd.txt'
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)
    
    # 运行NGSPICE
    try:
        subprocess.run(
            "ngspice -b temp_bsim3_idvd.cir",
            shell=True, check=True, capture_output=True, text=True
        )
    except subprocess.CalledProcessError as e:
        print(f"NGSPICE failed during BSIM3 IdVd data generation:\n{e.stderr}")
        return
    
    # 处理输出数据
    raw_data = np.loadtxt(temp_data_file)
    vds_real = raw_data[:, 0]
    id_real = np.abs(raw_data[:, 1])  # 取绝对值确保正电流
    
    # 添加少量噪声
    noise_scale = np.abs(id_real) * 0.03 + 1e-12
    id_real_noisy = id_real + np.random.normal(0, noise_scale)
    
    # 确保电流为正
    id_real_noisy = np.abs(id_real_noisy)
    
    # 保存数据
    df = pd.DataFrame({'Vds': vds_real, 'Id': id_real_noisy})
    df.to_csv(output_csv, index=False)
    
    print(f"成功生成BSIM3 IdVd数据: {len(df)} 个数据点")
    print(f"Vds范围: {vds_real.min():.1f} to {vds_real.max():.1f} V")
    print(f"Id范围: {id_real_noisy.min():.3e} to {id_real_noisy.max():.3e} A")
    print(f"数据保存到: {output_csv}")
    
    # 清理临时文件
    for temp_file in ['temp_bsim3_idvd.cir', temp_data_file]:
        if os.path.exists(temp_file):
            os.remove(temp_file)

def main():
    """主函数"""
    print("🔧 生成BSIM3模型目标数据")
    print("=" * 50)
    
    # 生成BSIM3 IdVg数据
    generate_bsim3_idvg_data()
    
    # 生成BSIM3 IdVd数据
    generate_bsim3_idvd_data()
    
    print("\n" + "=" * 50)
    print("✅ BSIM3目标数据生成完成!")
    print("\n生成的文件:")
    print("  📊 real_data_bsim3_idvg.csv - BSIM3 IdVg数据")
    print("  📊 real_data_bsim3_idvd.csv - BSIM3 IdVd数据")
    print("\n使用方法:")
    print("  在GUI中选择BSIM3模型时，加载对应的BSIM3数据文件")
    print("  这样可以确保模型和数据的一致性，提高拟合效果")

if __name__ == "__main__":
    main()
