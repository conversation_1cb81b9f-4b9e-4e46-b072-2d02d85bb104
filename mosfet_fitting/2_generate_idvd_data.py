import subprocess
import numpy as np
import pandas as pd
import os

def generate_idvd_target_data(cir_file='target_device_idvd.cir', output_csv='real_data_idvd.csv'):
    """
    Runs a single NGSPICE simulation to generate the "real" or "measured"
    IdVd data that we will try to fit our model to.
    """
    print("--- Step 1: Generating 'Real' IdVd Measurement Data ---")
    
    temp_data_file = 'temp_data_idvd.txt'
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)

    # Run NGSPICE
    try:
        subprocess.run(
            f"ngspice -b {cir_file}",
            shell=True, check=True, capture_output=True, text=True
        )
    except subprocess.CalledProcessError as e:
        print(f"NGSPICE failed during IdVd real data generation:\n{e.stderr}")
        return

    # Process the output from NGSPICE
    raw_data = np.loadtxt(temp_data_file)
    vds_real = raw_data[:, 0]
    id_real = -raw_data[:, 1]  # Invert current

    # Add a little noise to make it more realistic
    id_real_noisy = id_real + np.random.normal(0, id_real * 0.02)

    # Save to a clean CSV file
    df = pd.DataFrame({'Vds': vds_real, 'Id': id_real_noisy})
    df.to_csv(output_csv, index=False)
    
    print(f"Successfully generated and saved IdVd target data to '{output_csv}'")
    os.remove(temp_data_file)  # Clean up

def generate_multiple_idvd_curves(vgs_values=[3.0, 4.0, 5.0, 6.0], output_csv='real_data_idvd_multi.csv'):
    """
    Generate multiple IdVd curves at different Vgs values
    """
    print("--- Generating Multiple IdVd Curves ---")
    
    all_data = []
    
    for vgs in vgs_values:
        print(f"Generating IdVd curve at Vgs = {vgs}V")
        
        # Create temporary circuit file with specific Vgs
        temp_cir = f"temp_idvd_vgs{vgs}.cir"
        temp_data = f"temp_data_idvd_vgs{vgs}.txt"
        
        cir_content = f"""* Target Device for IdVd at Vgs={vgs}V
.title Target Power MOSFET for IdVd Characterization

.SUBCKT TargetMOSFET D G S
    .MODEL MOS_TARGET NMOS (
    + LEVEL = 1
    + VTO = 2.8
    + KP = 0.12
    + LAMBDA = 0.01
    )
    M1 D G S S MOS_TARGET L=1u W=10u
.ENDS TargetMOSFET

X1 D_node G_node 0 TargetMOSFET
Vds D_node 0 DC 0V
Vgs G_node 0 DC {vgs}V

.DC Vds 0 20 0.5

.control
    run
    wrdata {temp_data} v(d_node) I(Vds)
.endc
.end"""
        
        with open(temp_cir, 'w') as f:
            f.write(cir_content)
        
        try:
            subprocess.run(
                f"ngspice -b {temp_cir}",
                shell=True, check=True, capture_output=True, text=True
            )
            
            raw_data = np.loadtxt(temp_data)
            vds_real = raw_data[:, 0]
            id_real = -raw_data[:, 1]
            
            # Add noise
            id_real_noisy = id_real + np.random.normal(0, id_real * 0.02)
            
            # Create DataFrame for this Vgs
            df_vgs = pd.DataFrame({
                'Vgs': vgs,
                'Vds': vds_real, 
                'Id': id_real_noisy
            })
            all_data.append(df_vgs)
            
        except subprocess.CalledProcessError as e:
            print(f"NGSPICE failed for Vgs={vgs}V:\n{e.stderr}")
        finally:
            # Clean up temporary files
            if os.path.exists(temp_cir):
                os.remove(temp_cir)
            if os.path.exists(temp_data):
                os.remove(temp_data)
    
    if all_data:
        # Combine all data
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_df.to_csv(output_csv, index=False)
        print(f"Successfully generated multi-Vgs IdVd data to '{output_csv}'")
    else:
        print("Failed to generate any IdVd data")

if __name__ == "__main__":
    # Generate single IdVd curve
    generate_idvd_target_data()
    
    # Generate multiple IdVd curves at different Vgs values
    generate_multiple_idvd_curves()
