#!/usr/bin/env python3
"""
BSIM3拟合问题诊断脚本
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import fitting_core
import config
import data_utils
from exceptions import FittingError

def test_bsim3_simulation():
    """测试BSIM3仿真是否正常工作"""
    print("🔍 测试BSIM3仿真功能")
    print("=" * 50)
    
    # 测试BSIM3 IdVg仿真
    print("1. 测试BSIM3 IdVg仿真...")
    try:
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        # 使用默认BSIM3参数
        sim_params = {
            'vth0': 2.8,
            'u0': 0.06,
            'vsat': 1e5,
            'k1': 0.5,
            'k2': 0.0,
            'eta0': 0.08,
            'rdsw': 100
        }
        
        sweep_params = (0.0, 10.0, 0.2)
        fixed_voltage = 15.0
        
        vgs_sim, id_sim = fitting_core.run_spice_simulation(
            template, sim_params, sweep_params, fixed_voltage, "idvg"
        )
        
        if len(vgs_sim) > 0:
            print(f"   ✅ IdVg仿真成功: {len(vgs_sim)} 个数据点")
            print(f"   Vgs范围: {vgs_sim.min():.1f} to {vgs_sim.max():.1f} V")
            print(f"   Id范围: {id_sim.min():.3e} to {id_sim.max():.3e} A")
            
            # 检查是否有异常值
            if np.any(np.isnan(id_sim)) or np.any(np.isinf(id_sim)):
                print("   ⚠️ 发现NaN或Inf值")
            if np.any(id_sim < 0):
                print("   ⚠️ 发现负电流值")
        else:
            print("   ❌ IdVg仿真失败")
            return False
            
    except Exception as e:
        print(f"   ❌ IdVg仿真出错: {e}")
        return False
    
    # 测试BSIM3 IdVd仿真
    print("\n2. 测试BSIM3 IdVd仿真...")
    try:
        template = fitting_core.get_model_template("BSIM3", "idvd")
        
        sweep_params = (0.0, 20.0, 0.5)
        fixed_voltage = 5.0
        
        vds_sim, id_sim = fitting_core.run_spice_simulation(
            template, sim_params, sweep_params, fixed_voltage, "idvd"
        )
        
        if len(vds_sim) > 0:
            print(f"   ✅ IdVd仿真成功: {len(vds_sim)} 个数据点")
            print(f"   Vds范围: {vds_sim.min():.1f} to {vds_sim.max():.1f} V")
            print(f"   Id范围: {id_sim.min():.3e} to {id_sim.max():.3e} A")
            
            # 检查是否有异常值
            if np.any(np.isnan(id_sim)) or np.any(np.isinf(id_sim)):
                print("   ⚠️ 发现NaN或Inf值")
            if np.any(id_sim < 0):
                print("   ⚠️ 发现负电流值")
        else:
            print("   ❌ IdVd仿真失败")
            return False
            
    except Exception as e:
        print(f"   ❌ IdVd仿真出错: {e}")
        return False
    
    return True

def test_bsim3_parameter_sensitivity():
    """测试BSIM3参数敏感性"""
    print("\n🔬 测试BSIM3参数敏感性")
    print("=" * 50)
    
    template = fitting_core.get_model_template("BSIM3", "idvg")
    sweep_params = (0.0, 10.0, 0.5)
    fixed_voltage = 15.0
    
    # 基准参数
    base_params = {
        'vth0': 2.8,
        'u0': 0.06,
        'vsat': 1e5,
        'k1': 0.5,
        'k2': 0.0,
        'eta0': 0.08,
        'rdsw': 100
    }
    
    # 测试每个参数的影响
    param_tests = {
        'vth0': [1.0, 2.8, 4.0],
        'u0': [0.01, 0.06, 0.1],
        'vsat': [1e4, 1e5, 5e5],
        'k1': [0.1, 0.5, 2.0],
        'eta0': [0.01, 0.08, 0.3],
        'rdsw': [10, 100, 500]
    }
    
    results = {}
    
    for param_name, values in param_tests.items():
        print(f"\n测试参数 {param_name}:")
        param_results = []
        
        for value in values:
            test_params = base_params.copy()
            test_params[param_name] = value
            
            try:
                vgs_sim, id_sim = fitting_core.run_spice_simulation(
                    template, test_params, sweep_params, fixed_voltage, "idvg"
                )
                
                if len(id_sim) > 0:
                    max_current = id_sim.max()
                    param_results.append(max_current)
                    print(f"   {param_name}={value}: 最大电流 = {max_current:.3e} A")
                else:
                    param_results.append(0)
                    print(f"   {param_name}={value}: 仿真失败")
                    
            except Exception as e:
                param_results.append(0)
                print(f"   {param_name}={value}: 错误 - {e}")
        
        results[param_name] = param_results
    
    return results

def test_bsim3_fitting():
    """测试BSIM3拟合功能"""
    print("\n🎯 测试BSIM3拟合功能")
    print("=" * 50)
    
    # 生成BSIM3目标数据
    print("1. 生成BSIM3目标数据...")
    
    # 使用已知参数生成"真实"数据
    true_params = {
        'vth0': 3.0,
        'u0': 0.05,
        'vsat': 8e4,
        'k1': 0.6,
        'k2': 0.02,
        'eta0': 0.1,
        'rdsw': 150
    }
    
    template = fitting_core.get_model_template("BSIM3", "idvg")
    sweep_params = (0.0, 10.0, 0.5)
    fixed_voltage = 15.0
    
    try:
        vgs_true, id_true = fitting_core.run_spice_simulation(
            template, true_params, sweep_params, fixed_voltage, "idvg"
        )
        
        if len(vgs_true) == 0:
            print("   ❌ 无法生成目标数据")
            return False
        
        # 添加噪声
        noise_scale = np.abs(id_true) * 0.05 + 1e-12
        id_noisy = id_true + np.random.normal(0, noise_scale)
        
        # 创建DataFrame
        target_data = pd.DataFrame({'Vgs': vgs_true, 'Id': id_noisy})
        print(f"   ✅ 生成了 {len(target_data)} 个目标数据点")
        
    except Exception as e:
        print(f"   ❌ 生成目标数据失败: {e}")
        return False
    
    # 执行拟合
    print("\n2. 执行BSIM3拟合...")
    
    # 初始猜测参数（故意偏离真实值）
    initial_params = {
        'vth0': 2.5,
        'u0': 0.07,
        'vsat': 1.2e5,
        'k1': 0.4,
        'k2': -0.01,
        'eta0': 0.06,
        'rdsw': 80
    }
    
    # 设置拟合参数（只拟合几个关键参数）
    params_to_fit = [
        {'name': 'vth0', 'initial': 2.5, 'lower': 1.0, 'upper': 5.0},
        {'name': 'u0', 'initial': 0.07, 'lower': 0.01, 'upper': 0.15},
        {'name': 'vsat', 'initial': 1.2e5, 'lower': 5e4, 'upper': 3e5}
    ]
    
    try:
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, target_data, fixed_voltage, sweep_params,
            params_to_fit, initial_params, "idvg"
        )
        
        print("   ✅ 拟合完成")
        print("\n   拟合结果对比:")
        print("   参数名    真实值      初始值      拟合值")
        print("   " + "-" * 45)
        
        for param in params_to_fit:
            name = param['name']
            true_val = true_params[name]
            init_val = param['initial']
            fit_val = final_params[name]
            print(f"   {name:<8} {true_val:>10.3e} {init_val:>10.3e} {fit_val:>10.3e}")
        
        # 计算拟合质量
        id_fit_interp = data_utils.interpolate_data(vgs_true, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_true, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_true, id_fit_interp)
        
        print(f"\n   拟合质量: RMSE = {rmse:.3e} A, R² = {r_squared:.6f}")
        
        return True, (vgs_true, id_true, vgs_fit, id_fit, final_params)
        
    except Exception as e:
        print(f"   ❌ 拟合失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def plot_bsim3_results(results):
    """绘制BSIM3测试结果"""
    if results is None:
        return
    
    vgs_true, id_true, vgs_fit, id_fit, final_params = results
    
    plt.figure(figsize=(12, 8))
    
    # 主图
    plt.subplot(2, 2, 1)
    plt.semilogy(vgs_true, id_true, 'bo', markersize=4, label='Target Data')
    plt.semilogy(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
    plt.xlabel('Vgs (V)')
    plt.ylabel('Id (A)')
    plt.title('BSIM3 IdVg Fitting Results')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 线性图
    plt.subplot(2, 2, 2)
    plt.plot(vgs_true, id_true, 'bo', markersize=4, label='Target Data')
    plt.plot(vgs_fit, id_fit, 'r-', linewidth=2, label='BSIM3 Fitted')
    plt.xlabel('Vgs (V)')
    plt.ylabel('Id (A)')
    plt.title('BSIM3 IdVg Fitting (Linear Scale)')
    plt.grid(True, alpha=0.3)
    plt.legend()
    
    # 残差图
    plt.subplot(2, 2, 3)
    id_fit_interp = data_utils.interpolate_data(vgs_true, vgs_fit, id_fit)
    residuals = id_fit_interp - id_true
    plt.plot(vgs_true, residuals, 'go', markersize=3)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
    plt.xlabel('Vgs (V)')
    plt.ylabel('Residuals (A)')
    plt.title('Fitting Residuals')
    plt.grid(True, alpha=0.3)
    
    # 参数对比
    plt.subplot(2, 2, 4)
    param_names = list(final_params.keys())[:6]  # 显示前6个参数
    param_values = [final_params[name] for name in param_names]
    
    plt.bar(range(len(param_names)), param_values)
    plt.xticks(range(len(param_names)), param_names, rotation=45)
    plt.ylabel('Parameter Value')
    plt.title('Fitted Parameters')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('bsim3_debug_results.png', dpi=150, bbox_inches='tight')
    print("   📊 结果图表已保存为 'bsim3_debug_results.png'")

def main():
    """主诊断函数"""
    print("🔧 BSIM3拟合问题诊断")
    print("=" * 60)
    
    # 测试仿真功能
    sim_ok = test_bsim3_simulation()
    
    if not sim_ok:
        print("\n❌ BSIM3仿真存在问题，无法继续测试拟合")
        return
    
    # 测试参数敏感性
    sensitivity_results = test_bsim3_parameter_sensitivity()
    
    # 测试拟合功能
    fitting_ok, fitting_results = test_bsim3_fitting()
    
    if fitting_ok:
        plot_bsim3_results(fitting_results)
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    print(f"   仿真功能: {'✅ 正常' if sim_ok else '❌ 异常'}")
    print(f"   拟合功能: {'✅ 正常' if fitting_ok else '❌ 异常'}")
    
    if not fitting_ok:
        print("\n🔧 可能的问题和解决方案:")
        print("   1. BSIM3参数初始值可能不合理")
        print("   2. 参数边界设置可能过于严格")
        print("   3. SPICE模型可能需要更多的固定参数")
        print("   4. 拟合算法可能需要调整")
        print("   5. 目标数据可能存在问题")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n诊断被用户中断")
    except Exception as e:
        print(f"\n诊断出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("诊断程序退出")
