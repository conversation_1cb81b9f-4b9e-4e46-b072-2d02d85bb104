#!/usr/bin/env python3
"""
简化的BSIM3拟合测试 - 使用保守策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import fitting_core
import config
import data_utils

def test_bsim3_conservative():
    """使用保守策略测试BSIM3拟合"""
    print("🧪 BSIM3保守拟合策略测试")
    print("=" * 50)
    
    # 检查数据文件
    try:
        idvg_data = pd.read_csv('real_data_bsim3_idvg.csv')
        print(f"✅ 加载BSIM3 IdVg数据: {len(idvg_data)} 个数据点")
    except FileNotFoundError:
        print("❌ 缺少BSIM3数据，正在生成...")
        import subprocess
        subprocess.run(['uv', 'run', 'python', '3_generate_bsim3_data.py'])
        idvg_data = pd.read_csv('real_data_bsim3_idvg.csv')
    
    # 策略1：只拟合VTH0
    print("\n1. 策略1：只拟合VTH0参数")
    test_single_parameter_fitting(idvg_data, 'vth0')
    
    # 策略2：只拟合U0
    print("\n2. 策略2：只拟合U0参数")
    test_single_parameter_fitting(idvg_data, 'u0')
    
    # 策略3：拟合VTH0 + U0（紧边界）
    print("\n3. 策略3：拟合VTH0 + U0（紧边界）")
    test_dual_parameter_fitting(idvg_data)

def test_single_parameter_fitting(data, param_name):
    """测试单参数拟合"""
    try:
        # 获取BSIM3参数配置
        model_params = config.get_model_params("BSIM3")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置单个拟合参数
        if param_name == 'vth0':
            params_to_fit = [
                {'name': 'vth0', 'initial': 3.0, 'lower': 3.1, 'upper': 3.3}  # 很紧的边界
            ]
        elif param_name == 'u0':
            params_to_fit = [
                {'name': 'u0', 'initial': 0.055, 'lower': 0.050, 'upper': 0.060}  # 很紧的边界
            ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        # 计算拟合质量
        vgs_real = data['Vgs'].values
        id_real = data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"   ✅ 单参数拟合成功")
        print(f"   拟合参数: {param_name} = {final_params[param_name]:.6f}")
        print(f"   拟合质量: RMSE = {rmse:.6f}, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        plt.semilogy(vgs_real, id_real, 'bo', markersize=4, label='BSIM3 Data')
        plt.semilogy(vgs_fit, id_fit, 'r-', linewidth=2, label=f'Fitted ({param_name})')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title(f'BSIM3 Single Parameter Fitting ({param_name})')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        residuals = id_fit_interp - id_real
        plt.plot(vgs_real, residuals, 'go', markersize=3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('Vgs (V)')
        plt.ylabel('Residuals (A)')
        plt.title('Fitting Residuals')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'bsim3_single_{param_name}.png', dpi=150, bbox_inches='tight')
        print(f"   📊 结果保存为 'bsim3_single_{param_name}.png'")
        
        return True, rmse, r_squared
        
    except Exception as e:
        print(f"   ❌ 单参数拟合失败: {e}")
        return False, float('inf'), -float('inf')

def test_dual_parameter_fitting(data):
    """测试双参数拟合（紧边界）"""
    try:
        # 获取BSIM3参数配置
        model_params = config.get_model_params("BSIM3")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置双参数拟合（很紧的边界）
        params_to_fit = [
            {'name': 'vth0', 'initial': 3.2, 'lower': 3.15, 'upper': 3.25},  # ±1.5%
            {'name': 'u0', 'initial': 0.055, 'lower': 0.052, 'upper': 0.058}  # ±5%
        ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        # 计算拟合质量
        vgs_real = data['Vgs'].values
        id_real = data['Id'].values
        id_fit_interp = data_utils.interpolate_data(vgs_real, vgs_fit, id_fit)
        rmse = data_utils.calculate_rmse(id_real, id_fit_interp)
        r_squared = data_utils.calculate_r_squared(id_real, id_fit_interp)
        
        print(f"   ✅ 双参数拟合成功")
        print(f"   拟合参数:")
        print(f"     vth0 = {final_params['vth0']:.6f} (目标: 3.2)")
        print(f"     u0 = {final_params['u0']:.6f} (目标: 0.055)")
        print(f"   拟合质量: RMSE = {rmse:.6f}, R² = {r_squared:.6f}")
        
        # 绘制结果
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.semilogy(vgs_real, id_real, 'bo', markersize=4, label='BSIM3 Data')
        plt.semilogy(vgs_fit, id_fit, 'r-', linewidth=2, label='Fitted (VTH0+U0)')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 Dual Parameter Fitting')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 3, 2)
        plt.plot(vgs_real, id_real, 'bo', markersize=4, label='BSIM3 Data')
        plt.plot(vgs_fit, id_fit, 'r-', linewidth=2, label='Fitted (VTH0+U0)')
        plt.xlabel('Vgs (V)')
        plt.ylabel('Id (A)')
        plt.title('BSIM3 Dual Parameter Fitting (Linear)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        plt.subplot(1, 3, 3)
        residuals = id_fit_interp - id_real
        plt.plot(vgs_real, residuals, 'go', markersize=3)
        plt.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        plt.xlabel('Vgs (V)')
        plt.ylabel('Residuals (A)')
        plt.title('Fitting Residuals')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('bsim3_dual_params.png', dpi=150, bbox_inches='tight')
        print(f"   📊 结果保存为 'bsim3_dual_params.png'")
        
        return True, rmse, r_squared
        
    except Exception as e:
        print(f"   ❌ 双参数拟合失败: {e}")
        return False, float('inf'), -float('inf')

def provide_bsim3_recommendations():
    """提供BSIM3使用建议"""
    print("\n📋 BSIM3拟合建议")
    print("=" * 50)
    
    recommendations = """
🎯 推荐的BSIM3拟合策略:

1. 🥇 最佳策略（初学者）:
   • 只拟合 VTH0 参数
   • 边界范围: ±2% (例如: [3.14, 3.26])
   • 预期效果: 中等拟合质量，稳定收敛

2. 🥈 进阶策略:
   • 拟合 VTH0 + U0 参数
   • 边界范围: VTH0 ±1.5%, U0 ±5%
   • 预期效果: 较好拟合质量，可能需要多次尝试

3. 🥉 专家策略:
   • 拟合 VTH0 + U0 + VSAT 参数
   • 使用分阶段拟合方法
   • 需要深入理解BSIM3模型

⚠️ 避免的做法:
   ❌ 同时拟合超过3个参数
   ❌ 使用过宽的参数边界
   ❌ 用LEVEL=1数据拟合BSIM3模型

💡 提高成功率的技巧:
   ✅ 使用BSIM3专用数据文件
   ✅ 从单参数开始，逐步增加
   ✅ 基于物理意义设置合理边界
   ✅ 检查拟合结果的物理合理性
    """
    
    print(recommendations)

def main():
    """主测试函数"""
    print("🔧 BSIM3简化拟合测试")
    print("=" * 60)
    
    # 执行保守拟合测试
    test_bsim3_conservative()
    
    # 提供使用建议
    provide_bsim3_recommendations()
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("BSIM3拟合需要谨慎的参数选择和边界设置。")
    print("建议从单参数拟合开始，逐步增加复杂度。")
    print("使用专用的BSIM3数据文件可以显著提高拟合效果。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("测试程序退出")
