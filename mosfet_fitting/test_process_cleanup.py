#!/usr/bin/env python3
"""
测试进程清理功能
"""

import subprocess
import time
import os
import signal
import sys

def test_gui_process_cleanup():
    """测试GUI进程清理功能"""
    print("🧪 测试GUI进程清理功能")
    print("=" * 50)
    
    # 启动GUI进程
    print("1. 启动GUI进程...")
    gui_process = subprocess.Popen(
        ['uv', 'run', 'python', 'gui_app.py'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    print(f"   GUI进程已启动，PID: {gui_process.pid}")
    
    # 等待GUI启动
    time.sleep(2)
    
    # 检查进程是否运行
    if gui_process.poll() is None:
        print("   ✅ GUI进程正在运行")
    else:
        print("   ❌ GUI进程启动失败")
        return
    
    # 模拟用户关闭窗口（发送SIGTERM信号）
    print("\n2. 模拟关闭GUI窗口...")
    try:
        # 发送终止信号
        gui_process.terminate()
        
        # 等待进程结束
        try:
            gui_process.wait(timeout=5)
            print("   ✅ GUI进程正常终止")
        except subprocess.TimeoutExpired:
            print("   ⚠️ GUI进程未在5秒内终止，强制杀死...")
            gui_process.kill()
            gui_process.wait()
            print("   ✅ GUI进程已被强制终止")
            
    except Exception as e:
        print(f"   ❌ 终止进程时出错: {e}")
    
    # 检查进程是否完全退出
    print("\n3. 验证进程清理...")
    time.sleep(1)
    
    if gui_process.poll() is not None:
        print("   ✅ GUI进程已完全退出")
        print(f"   退出码: {gui_process.returncode}")
    else:
        print("   ❌ GUI进程仍在运行")
    
    # 检查是否有残留的子进程
    print("\n4. 检查残留进程...")
    try:
        result = subprocess.run(
            ['ps', 'aux'], 
            capture_output=True, 
            text=True
        )
        
        lines = result.stdout.split('\n')
        related_processes = []
        
        for line in lines:
            if any(keyword in line for keyword in ['gui_app.py', 'fitting_core', 'ngspice']):
                if str(os.getpid()) not in line:  # 排除当前进程
                    related_processes.append(line)
        
        if related_processes:
            print(f"   ⚠️ 发现 {len(related_processes)} 个可能的残留进程:")
            for proc in related_processes:
                print(f"      {proc}")
        else:
            print("   ✅ 没有发现残留进程")
            
    except Exception as e:
        print(f"   ❌ 检查进程时出错: {e}")

def test_cleanup_tool():
    """测试清理工具"""
    print("\n" + "=" * 50)
    print("🛠️ 测试进程清理工具")
    print("=" * 50)
    
    # 检查清理工具是否存在
    if not os.path.exists('cleanup_processes.py'):
        print("❌ 清理工具不存在")
        return
    
    print("✅ 清理工具存在")
    
    # 测试清理工具的导入
    try:
        import cleanup_processes
        print("✅ 清理工具可以正常导入")
        
        # 测试查找进程功能
        processes = cleanup_processes.find_related_processes()
        print(f"✅ 查找到 {len(processes)} 个相关进程")
        
    except Exception as e:
        print(f"❌ 测试清理工具时出错: {e}")

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 50)
    print("📋 GUI进程清理使用说明")
    print("=" * 50)
    
    instructions = """
🎯 问题描述:
   tkinter GUI窗口关闭后，Python进程可能仍在后台运行

🔧 解决方案:
   1. 改进了GUI的on_closing方法，确保所有资源被正确清理
   2. 添加了信号处理器，处理Ctrl+C和系统终止信号
   3. 使用os._exit(0)强制退出，避免进程残留

🚀 使用方法:
   
   正常启动GUI:
   uv run python gui_app.py
   
   如果发现进程残留，使用清理工具:
   uv run python cleanup_processes.py
   
   实时监控进程:
   uv run python cleanup_processes.py --monitor

🛡️ 安全退出方式:
   1. 点击窗口的X按钮 (推荐)
   2. 在终端按Ctrl+C
   3. 使用系统任务管理器

🔍 验证方法:
   1. 关闭GUI后检查终端是否返回命令提示符
   2. 使用 ps aux | grep python 检查是否有残留进程
   3. 使用清理工具扫描相关进程

⚡ 改进内容:
   ✅ 安全的进程终止
   ✅ matplotlib资源清理
   ✅ 多进程队列清理
   ✅ 信号处理器
   ✅ 强制退出机制
   ✅ 专用清理工具
    """
    
    print(instructions)

def main():
    """主测试函数"""
    print("🧪 GUI进程清理测试套件")
    print("=" * 50)
    
    # 询问用户要执行的测试
    print("选择测试项目:")
    print("1. 测试GUI进程清理功能")
    print("2. 测试清理工具")
    print("3. 显示使用说明")
    print("4. 全部测试")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        test_gui_process_cleanup()
    elif choice == '2':
        test_cleanup_tool()
    elif choice == '3':
        show_usage_instructions()
    elif choice == '4':
        test_gui_process_cleanup()
        test_cleanup_tool()
        show_usage_instructions()
    else:
        print("无效选择")
        return
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
    finally:
        print("测试程序退出")
