# MOSFET参数拟合工具

一个基于ngspice和least squares算法的MOSFET器件参数自动化拟合工具，支持IdVd和IdVg特性曲线拟合，并输出SPICE模型参数。

## 🚀 主要功能

- **多种拟合模式**: IdVg拟合、IdVd拟合、联合拟合
- **SPICE模型支持**: LEVEL=1和BSIM3模型
- **双界面**: GUI图形界面和CLI命令行工具
- **数据处理**: 自动数据验证、清理和可视化
- **测试验证**: 完整的测试套件确保代码质量

## 📦 安装和依赖

### 系统要求
- Python 3.13+
- ngspice仿真器
- uv包管理器

### 安装依赖
```bash
# 使用uv安装Python依赖
uv sync

# 或手动安装
pip install numpy pandas scipy matplotlib
```

### 安装ngspice
```bash
# Ubuntu/Debian
sudo apt-get install ngspice

# macOS
brew install ngspice

# Windows
# 下载并安装ngspice from http://ngspice.sourceforge.net/
```

## 🎯 快速开始

### 1. 生成测试数据
```bash
# 生成IdVg数据
uv run python 1_generate_real_data.py

# 生成IdVd数据
uv run python 2_generate_idvd_data.py
```

### 2. 使用GUI界面
```bash
uv run python gui_app.py
```

### 3. 使用命令行工具
```bash
# IdVg拟合
uv run python main.py --mode idvg --verbose --plot

# IdVd拟合
uv run python main.py --mode idvd --verbose --plot

# 查看帮助
uv run python main.py --help
```

### 4. 运行演示
```bash
# 完整功能演示
uv run python demo.py
```

### 5. 运行测试
```bash
# 运行测试套件
uv run python test_suite.py
```

## 📊 使用示例

### 编程接口
```python
import fitting_core
import data_utils

# 加载数据
data = data_utils.load_measurement_data('real_data_idvd.csv', 'idvd')

# 设置参数
model_type = "LEVEL=1"
params_to_fit = [
    {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
    {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
]

# 执行拟合
template = fitting_core.get_model_template(model_type, "idvd")
final_params, voltage_fit, id_fit = fitting_core.perform_fitting(
    template, data, 5.0, (0.0, 20.0, 0.5),
    params_to_fit, {'vto': 2.5, 'kp': 0.1}, "idvd"
)

print("拟合结果:", final_params)
```

### 联合拟合
```python
# 联合拟合使用IdVg和IdVd数据
final_params, idvg_result, idvd_result = fitting_core.perform_joint_fitting(
    model_type, idvg_data, idvd_data,
    15.0, (0.0, 10.0, 0.2),  # IdVg参数
    5.0, (0.0, 20.0, 0.5),   # IdVd参数
    params_to_fit, initial_params
)
```

## 📁 项目结构

```
mosfet_fitting/
├── fitting_core.py          # 核心拟合算法
├── gui_app.py              # GUI应用程序
├── main.py                 # 命令行主程序
├── config.py               # 配置管理
├── data_utils.py           # 数据处理工具
├── exceptions.py           # 自定义异常
├── test_suite.py           # 测试套件
├── demo.py                 # 功能演示脚本
├── 1_generate_real_data.py # IdVg数据生成器
├── 2_generate_idvd_data.py # IdVd数据生成器
├── target_device.cir       # IdVg目标器件
├── target_device_idvd.cir  # IdVd目标器件
└── 项目优化总结.md         # 详细技术文档
```

## 🔧 配置选项

### 模型参数
- **LEVEL=1**: VTO (阈值电压), KP (跨导参数)
- **BSIM3**: VTH0, U0, VSAT, K1, K2, ETA0, RDSW

### 仿真参数
- **IdVg**: 固定Vds，扫描Vgs
- **IdVd**: 固定Vgs，扫描Vds

### 拟合算法
- 方法: Trust Region Reflective (TRF)
- 边界约束支持
- 实时进度反馈

## 📈 性能指标

### 拟合精度
- **IdVg拟合**: RMSE ≈ 0.24 A, R² ≈ 0.9995
- **IdVd拟合**: RMSE ≈ 5.14 A, R² ≈ 0.245
- **联合拟合**: 综合优化两种特性

### 运行时间
- 单次拟合: 10-30秒
- 联合拟合: 30-60秒
- 数据生成: 1-2秒

## 🧪 测试

项目包含完整的测试套件：

```bash
# 运行所有测试
uv run python test_suite.py

# 测试覆盖范围
- 核心功能模块测试
- 数据处理和验证测试
- 配置管理测试
- 端到端集成测试
```

## 📚 文档

- [项目优化总结](项目优化总结.md) - 详细技术文档
- [思维导图](思维导图.md) - 项目架构图
- [项目总结与展望](项目总结与展望.md) - 原始项目文档

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置
```bash
# 克隆项目
git clone <repository-url>
cd mosfet_fitting

# 安装依赖
uv sync

# 运行测试
uv run python test_suite.py
```

## 📄 许可证

本项目采用MIT许可证。

## 🙏 致谢

- ngspice开源仿真器
- SciPy优化算法库
- Python科学计算生态系统

---

**作者**: Augment Agent
**版本**: 2.0
**更新日期**: 2025-07-14