#!/usr/bin/env python3
"""
测试Power NMOS格式的模型输出
"""

import pandas as pd
import fitting_core
import config
import data_utils

def test_power_nmos_model_generation():
    """测试Power NMOS格式模型生成"""
    print("🔧 测试Power NMOS格式模型生成")
    print("=" * 50)
    
    # 测试LEVEL=1拟合和模型生成
    print("1. 测试LEVEL=1模型...")
    try:
        # 加载数据
        idvg_data = pd.read_csv('real_data.csv')
        print(f"   加载数据: {len(idvg_data)} 个数据点")
        
        # 获取参数配置
        model_params = config.get_model_params("LEVEL=1")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置拟合参数
        params_to_fit = [
            {'name': 'vto', 'initial': 2.5, 'lower': 1.0, 'upper': 4.0},
            {'name': 'kp', 'initial': 0.1, 'lower': 0.01, 'upper': 0.5}
        ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("LEVEL=1", "idvg")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, idvg_data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        print(f"   ✅ 拟合完成")
        print(f"   拟合参数: VTO={final_params['vto']:.6f}, KP={final_params['kp']:.6f}")
        
        # 生成Power NMOS格式模型
        data_utils.export_results(final_params, "LEVEL=1", "idvg", "fitted_power_nmos_level1.lib")
        print(f"   ✅ 模型已导出到: fitted_power_nmos_level1.lib")
        
    except Exception as e:
        print(f"   ❌ LEVEL=1测试失败: {e}")
    
    # 测试BSIM3拟合和模型生成
    print("\n2. 测试BSIM3模型...")
    try:
        # 检查BSIM3数据是否存在
        try:
            bsim3_data = pd.read_csv('real_data_bsim3_idvg.csv')
        except FileNotFoundError:
            print("   生成BSIM3数据...")
            import subprocess
            subprocess.run(['uv', 'run', 'python', '3_generate_bsim3_data.py'])
            bsim3_data = pd.read_csv('real_data_bsim3_idvg.csv')
        
        print(f"   加载BSIM3数据: {len(bsim3_data)} 个数据点")
        
        # 获取BSIM3参数配置
        model_params = config.get_model_params("BSIM3")
        all_initial_params = {name: params['initial'] for name, params in model_params.items()}
        
        # 设置拟合参数（保守策略）
        params_to_fit = [
            {'name': 'vth0', 'initial': 3.2, 'lower': 3.0, 'upper': 3.4},
            {'name': 'u0', 'initial': 0.055, 'lower': 0.050, 'upper': 0.060}
        ]
        
        # 仿真参数
        sim_params = config.get_simulation_params("idvg")
        template = fitting_core.get_model_template("BSIM3", "idvg")
        
        # 执行拟合
        final_params, vgs_fit, id_fit = fitting_core.perform_fitting(
            template, bsim3_data, sim_params['vds'],
            (sim_params['vgs_start'], sim_params['vgs_stop'], sim_params['vgs_step']),
            params_to_fit, all_initial_params, "idvg"
        )
        
        print(f"   ✅ 拟合完成")
        print(f"   拟合参数: VTH0={final_params['vth0']:.6f}, U0={final_params['u0']:.6f}")
        
        # 生成Power NMOS格式模型
        data_utils.export_results(final_params, "BSIM3", "idvg", "fitted_power_nmos_bsim3.lib")
        print(f"   ✅ 模型已导出到: fitted_power_nmos_bsim3.lib")
        
    except Exception as e:
        print(f"   ❌ BSIM3测试失败: {e}")

def compare_model_formats():
    """比较新旧模型格式"""
    print("\n📊 模型格式对比")
    print("=" * 50)
    
    print("原始模板特点:")
    print("  ✅ 完整的Power NMOS结构")
    print("  ✅ 寄生电感和电阻")
    print("  ✅ 电压相关电容模型")
    print("  ✅ 体二极管和栅源二极管")
    print("  ✅ 专业的功率器件建模")
    
    print("\n新生成模型特点:")
    print("  ✅ 保持完整的电路结构")
    print("  ✅ 只修改拟合得到的参数")
    print("  ✅ 保留所有寄生元件")
    print("  ✅ 兼容原始模板格式")
    print("  ✅ 添加拟合信息注释")

def show_usage_instructions():
    """显示使用说明"""
    print("\n📋 使用说明")
    print("=" * 50)
    
    instructions = """
🎯 Power NMOS模型生成流程:

1. 数据准备:
   • LEVEL=1: 使用 real_data.csv 或 real_data_idvd.csv
   • BSIM3: 使用 real_data_bsim3_idvg.csv 或 real_data_bsim3_idvd.csv

2. 执行拟合:
   • GUI方式: 运行 gui_app.py，选择模型和数据
   • 命令行: 运行 main.py --mode idvg --verbose

3. 模型输出:
   • 自动生成Power NMOS格式的.lib文件
   • 包含完整的寄生元件和电容模型
   • 只修改拟合得到的核心参数

4. 模型使用:
   • 在SPICE仿真器中包含生成的.lib文件
   • 使用子电路名称实例化器件
   • 连接D、G、S三个端子

🔧 生成的模型文件包含:
   ✅ 寄生电感: LD, LG, LS
   ✅ 寄生电阻: RD, RG, RS系列
   ✅ 主MOSFET: 使用拟合参数
   ✅ 体二极管: DBD模型
   ✅ 栅源二极管: DBGS模型
   ✅ 电压相关电容: Cgs, Cds, Cgd

⚠️ 注意事项:
   • 电容参数使用默认值，可通过C-V测量优化
   • 寄生参数基于典型功率器件，可根据实际器件调整
   • 二极管参数使用默认值，可通过反向特性测量优化
    """
    
    print(instructions)

def main():
    """主测试函数"""
    print("🔧 Power NMOS格式模型生成测试")
    print("=" * 60)
    
    # 执行测试
    test_power_nmos_model_generation()
    
    # 格式对比
    compare_model_formats()
    
    # 使用说明
    show_usage_instructions()
    
    print("\n" + "=" * 60)
    print("🎉 Power NMOS格式模型生成测试完成!")
    print("\n生成的文件:")
    import os
    files = ['fitted_power_nmos_level1.lib', 'fitted_power_nmos_bsim3.lib']
    for file in files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (未生成)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("测试程序退出")
