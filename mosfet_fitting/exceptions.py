"""
自定义异常类
"""

class FittingError(Exception):
    """拟合过程中的基础异常"""
    pass

class SimulationError(FittingError):
    """SPICE仿真异常"""
    def __init__(self, message, stderr_output=None):
        super().__init__(message)
        self.stderr_output = stderr_output

class ParameterError(FittingError):
    """参数验证异常"""
    pass

class DataError(FittingError):
    """数据相关异常"""
    pass

class TemplateError(FittingError):
    """模板相关异常"""
    pass

class ConvergenceError(FittingError):
    """拟合收敛异常"""
    pass
