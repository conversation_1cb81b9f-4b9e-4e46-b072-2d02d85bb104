.SUBCKT S6_40_c_var dd g s0 sp Tj PARAMS: a=1 Rsp=1 dVth=0 dR=0 dgfs=0 Inn=1
+Unn=1 Rmax=1 gmin=1 Rs=1 Rp=1 dC=0 Rm=1u

* Parameters from the parent subcircuit, needed for S6_40_c_var to run
.PARAM Rs=687u      Rg=2.3       Rd=0.004       Rm=150u
.PARAM Inn=20       Unn=10       Rmax=6.3m    gmin=38.53
.PARAM act=1.15     Rsp=1.8


.PARAM b0=200       p0=6.62        p1=-25.08m     p2=40.85u      mubet=1.42     fbet=0
.PARAM Vth0=2.5      c=945.03m      Fm=190m        Fn=600m        al=500m        auth=2.46m
.PARAM dvx=550m       dvgs=120m      auth_sub=1.81m

.PARAM Rd=0.004       nmu=3.63       Rf=719.1m

.PARAM lnIsj=-28      ndi=1          Rdi=1.98m      nmu2=698.99m   n_Isj=1        UB=42
.PARAM ab=21.5m       ab2=0          UT=100m        lB=-23         td=20n         ta=2n

.PARAM kbq=85.8u      Tref=298       T0=273

.PARAM f3=493.95p     f3a=60.19p

.PARAM f4=35.54p      f5=75.5p       sl=2.45p       ps1=55.89p     ps2=-184m      ps3=84.86p
.PARAM ps4=-2.42      ps5=2.32p      ps6=7.35p      ps7=791.94m    pc0=2.09p

.PARAM q83=2.85p      q84=0          qs6=2.03p      qs7=168.45p    qs8=-162.92m

.PARAM q80=325.29p    q81=358.56p    q82=18.43p     qs1=30.86p     qs2=234.87p    qs3=-27.91m
.PARAM f2r=0

.PARAM x1={(q80-q81)/q82}       x2={q80/q82}
.PARAM y1={(f4-f5)/sl}          y2={f4/sl}
.PARAM Vmin=2.16      Vmax=2.96      dCmax=330m
.PARAM Vth={Vth0+(Vmax-Vth0)*limit(dVth,0,1)-(Vmin-Vth0)*limit(dVth,-1,0)}
.PARAM q0={b0*((1-fbet)*(T0/Tref)**mubet+fbet)*a}
.PARAM q1={(Unn-Inn*Rs-Vth0)*q0}
.PARAM q2={(Fm*SQRT(0.4)-c)*Inn*q0}
.PARAM Rlim={(q1+2*q2*Rmax-SQRT(q1**2+4*q2))/(2*q2)}
.PARAM dRd={Rd/a+(dR==0 ? (dgfs==0 ? limit(dgfs,-1,0) : 0) : 0)*max(Rlim-Rd/a-Rs-Rp,0)}
.PARAM bm={c/((1/gmin-Rs)**2*Inn*a*((1-fbet)*(T0/Tref)**mubet+fbet))}
.PARAM bet={b0+(b0-bm)*(dR==0 ? (dVth==0 ? limit(dgfs,-1,0) : 0) : 0)}
.PARAM dC1={1+dCmax*limit(dC,0,1)}
.PARAM dC2={1+dCmax*limit(dC,0,1)}

.PARAM Cgs0={f3*a*dC1}
.PARAM Cgs1={f3a*a*dC1}

.PARAM Cds1={qs6*a*dC1}
.PARAM Cds2={qs7*a*dC1}
.PARAM Cds3={q83*a*dC1}
.PARAM Cds5={qs1*a*dC1}
.PARAM Cds6={(a*qs2*(1+f2r/sqrt(a)))*dC1}
.PARAM Cds8={q80*a*dC1}

.PARAM Cdg1={(a*ps1+pc0*sqrt(a))*dC2}
.PARAM Cdg2={ps3*a*dC2}
.PARAM Cdg3={(ps5*a+ps6)*dC2}
.PARAM Cdg4={f4*a*dC2}
.PARAM dRdi={Rdi/a}

.FUNC Ue(g,y,w)  {(g-Vth+auth*(w-Tref)+Fm*y**Fn)}
.FUNC Ue1(g,y,w) {Ue(g,y,w)+(1+limit(Ue(g,y,w)+dvx,0,1)**2*(2*limit(Ue(g,y,w)+dvx,0,1)-3))*(dvgs+(auth_sub-auth)*(w-Tref))}

.FUNC I0(Uee,p,pp,z1) {Uee>pp ? (Uee-c*z1)*z1 : p*(pp-p)/c*exp((Uee-pp)/p)}
.FUNC Ih(Uds,T,p,Uee) {bet*((1-fbet)*(T0/T)**mubet+fbet)*I0(Uee,p,min(2*p,p+c*Uds),min(Uds,Uee/(2*c)))}
.FUNC Jh(d,g,w,y,s,x) {a*((Ih(s*y+min(d,0),w,(p0+(p1+p2*w)*w)*kbq*w,Ue1(g,y,w))+exp(min(lB+(d-UB-ab*(w-Tref))/UT,24))))}

.FUNC Idiode(Usd,Tj,Iss) {exp(min(ln(Iss)+Usd/(ndi*kbq*Tj),7))-Iss}
.FUNC Idiod(Usd,Tj)      {a*Idiode(Usd,Tj,exp(min(lnIsj+(Tj/Tref-1)*1.12/(ndi*kbq*Tj),7))*(Tj/Tref)**n_Isj)}

.FUNC Pr(Vss0,Vssp)  {Vss0*Vss0/Rm+Vssp*Vssp/Rsp}
.FUNC Q01(Uds) {a*(limit(Uds,x1,x2)*(q80-q82/2*limit(Uds,x1,x2))+min(Uds-x1,0)*q81-max(x1,0)*(q80-q81)/2)}
.FUNC Q02(Udg) {a*(limit(Udg,y1,y2)*(f4-sl/2*limit(Udg,y1,y2))+min(Udg-y1,0)*f5-max(y1,0)*(f4-f5)/2)}

* --- Capacitance section commented out for stability ---
* C_Cgs  g      s      {Cgs0}
* C_Cgs1 g      sp     {Cgs1}
* 
* C_Cds1 d      s      {Cds1}
* E_Eds2 d      edep2  VALUE {V(d,s)-I(V_sense3)/Cds2}
* C_Cds2 edep2  s      {Cds2}
* E_Eds3 d      edep3  VALUE {q84==0 ? 0 : V(d,s)-(exp(q84*max(V(d,s),-1))-1)/min(q84,-1u)-min(V(d,s)+1,0)*exp(-q84)}
* C_Cds3 edep3  s      {Cds3}
* 
* C_Cds5 d      sp     {Cds5}
* E_Eds6 d      edep6  VALUE {qs3==0 ? 0 : V(d,sp)-(exp(qs3*max(V(d,sp),0))-1)/min(qs3,-1u)-min(V(d,sp),0)}
* C_Cds6 edep6  sp     {Cds6}
* E_Eds8 d      edep8  VALUE {V(d,sp)-Q01(V(d,sp))/Cds8}
* C_Cds8 edep8  sp     {Cds8}
* 
* E_Edg1 d    ox1 VALUE {ps2==0 ? 0 : V(d,g)-(exp(ps2*max(V(d,g),0))-1)/min(ps2,-1u)-min(V(d,g),0)}
* C_Cdg1 ox1  g   {Cdg1}
* E_Edg2 d    ox2 VALUE =
* +{ps4==0 ? 0 : V(d,g)-((exp(ps4*(max(V(d,g)+ps7,0)))-exp(ps4*max(ps7,0)))/min(ps4,-1u)+min(V(d,g)+max(ps7,0),max(0,-ps7))))}
* C_Cdg2 ox2  g   {Cdg2}
* C_Cdg3 d    g   {Cdg3}
* E_Edg4 d    ox4 VALUE {V(d,g)-Q02(V(d,g))/Cdg4}
* C_Cdg4 ox4  g   {Cdg4}

Rfp     s sp    {Rsp}

G_chan   d5a   s  VALUE={Jh(V(d5a,s),V(g,s),T0+limit(V(Tj),-200,300),(SQRT(1+4*al*max(V(d5a,s),0))-1)/2/al,sgn(V(d5a,s)),0)}
Rd06     d5a  d5  1u
V_sm     d    d5  0
G_RMos   d1    d  VALUE={V(d1,d)/(Rf*dRd+(1-Rf)*dRd*((limit(V(Tj),-200,999)+T0)/Tref)**nmu)}
V_sense  dd   d1  0
G_diode   s   d3  VALUE={Idiod(V(s,d3),T0+limit(V(Tj),-200,499))}
G_Rdio   d2   d1  VALUE={V(d2,d1)/(dRdi*((limit(V(Tj),-200,999)+T0)/Tref)**nmu2)}
V_sense2 d2   d3  0

* --- Thermal and other complex parts commented out ---
* L_L001 a c {td/(ta+td)}
* R_R001 a b {1/ta}
* V_sense3 c 0 0
* E_E001 b 0 VALUE {I(V_sense2)}
* E_E002 e 0 VALUE {Cds2*((exp(qs8*max(V(d,s),-1))-1)/min(qs8,-1u)-min(V(d,s)+1,0)*exp(-qs8))}
* R_R002 e c 1
* R_R003 a 0 500Meg

R1        g    s  1G
Rd01      d    s  500Meg
Rd02     d2    s  500Meg
Rd03     d1    d  1k
Rssp      g   sp  100Meg

Rmet      s    s0 {Rm}

* --- Thermal feedback commented out ---
* G_TH      0   Tj  VALUE =
* +{(I(V_sense)-I(V_sense2))*V(d1,d)+I(V_sm)*V(d,s)+I(V_sense2)*V(d1,s)+Pr(V(s,s0),V(s,sp))}



.ENDS S6_40_c_var
