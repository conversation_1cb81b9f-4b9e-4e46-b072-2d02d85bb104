* Target Device Definition and Test Bench for IdVd Characteristics
.title Target Power MOSFET for IdVd Characterization

* This file defines the "physical" device whose parameters we want to discover.
* We are "hiding" the true parameters here for IdVd curve generation.
.SUBCKT TargetMOSFET D G S
    .MODEL MOS_TARGET NMOS (
    + LEVEL = 1
    + VTO = 2.8     ; The "secret" true threshold voltage
    + KP = 0.12     ; The "secret" true transconductance
    + LAMBDA = 0.01 ; The "secret" channel length modulation
    )
    M1 D G S S MOS_TARGET L=1u W=10u
.ENDS TargetMOSFET

* Test Bench to get Id-Vds curve
X1 D_node G_node 0 TargetMOSFET
Vds D_node 0 DC 0V  ; Will be swept
Vgs G_node 0 DC 5V  ; Fixed gate voltage for IdVd curve

* DC Sweep - sweep Vds from 0 to 20V
.DC Vds 0 20 0.5

.control
    run
    wrdata temp_data_idvd.txt v(d_node) I(Vds)
.endc
.end
