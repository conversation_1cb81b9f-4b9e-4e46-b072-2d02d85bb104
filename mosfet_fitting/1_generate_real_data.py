import subprocess
import numpy as np
import pandas as pd
import os

def generate_target_data(cir_file='target_device.cir', output_csv='real_data.csv'):
    """
    Runs a single NGSPICE simulation to generate the "real" or "measured"
    data that we will try to fit our model to.
    """
    print("--- Step 1: Generating 'Real' Measurement Data ---")
    
    temp_data_file = 'temp_data.txt'
    if os.path.exists(temp_data_file):
        os.remove(temp_data_file)

    # Run NGSPICE
    try:
        subprocess.run(
            f"ngspice -b {cir_file}",
            shell=True, check=True, capture_output=True, text=True
        )
    except subprocess.CalledProcessError as e:
        print(f"NGSPICE failed during real data generation:\n{e.stderr}")
        return

    # Process the output from NGSPICE
    raw_data = np.loadtxt(temp_data_file)
    vgs_real = raw_data[:, 1]
    id_real = -raw_data[:, 3] # Invert current

    # Add a little noise to make it more realistic
    id_real_noisy = id_real + np.random.normal(0, id_real * 0.02)

    # Save to a clean CSV file
    df = pd.DataFrame({'Vgs': vgs_real, 'Id': id_real_noisy})
    df.to_csv(output_csv, index=False)
    
    print(f"Successfully generated and saved target data to '{output_csv}'")
    os.remove(temp_data_file) # Clean up

if __name__ == "__main__":
    generate_target_data()
